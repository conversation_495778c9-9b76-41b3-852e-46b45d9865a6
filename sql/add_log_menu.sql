-- 添加系统日志菜单
-- 在系统工具下添加系统日志菜单项

-- 获取系统工具菜单ID (parent_id = 3)
-- 插入系统日志菜单
INSERT INTO `sys_menu` VALUES (
    117, 
    '系统日志', 
    3, 
    4, 
    '/tool/log', 
    '', 
    'C', 
    '0', 
    '1', 
    'tool:log:view', 
    'fa fa-file-text-o', 
    'admin', 
    NOW(), 
    '', 
    NULL, 
    '系统日志菜单'
);

-- 获取刚插入的菜单ID作为父菜单ID
SET @parentId = 117;

-- 插入系统日志相关按钮权限
INSERT INTO `sys_menu` VALUES (
    1117, 
    '日志查询', 
    @parentId, 
    1, 
    '#', 
    '', 
    'F', 
    '0', 
    '1', 
    'tool:log:list', 
    '#', 
    'admin', 
    NOW(), 
    '', 
    NULL, 
    ''
);

INSERT INTO `sys_menu` VALUES (
    1118, 
    '日志下载', 
    @parentId, 
    2, 
    '#', 
    '', 
    'F', 
    '0', 
    '1', 
    'tool:log:download', 
    '#', 
    'admin', 
    NOW(), 
    '', 
    NULL, 
    ''
);

INSERT INTO `sys_menu` VALUES (
    1119, 
    '日志清理', 
    @parentId, 
    3, 
    '#', 
    '', 
    'F', 
    '0', 
    '1', 
    'tool:log:clean', 
    '#', 
    'admin', 
    NOW(), 
    '', 
    NULL, 
    ''
);
