<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ads.mapper.AdsClickRecordMapper">
    
    <resultMap type="AdsClickRecord" id="AdsClickRecordResult">
        <result property="recordId"        column="record_id"        />
        <result property="clickId"         column="click_id"         />
        <result property="offerId"         column="offer_id"         />
        <result property="publisherId"     column="publisher_id"     />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="adsId"           column="ads_id"           />
        <result property="deviceId"        column="device_id"        />
        <result property="ipAddress"       column="ip_address"       />
        <result property="userAgent"       column="user_agent"       />
        <result property="countryCode"     column="country_code"     />
        <result property="channel"         column="channel"          />
        <result property="subParam1"       column="sub_param1"       />
        <result property="subParam2"       column="sub_param2"       />
        <result property="subParam3"       column="sub_param3"       />
        <result property="targetUrl"       column="target_url"       />
        <result property="payout"          column="payout"           />
        <result property="clickTime"       column="click_time"       />
        <result property="status"          column="status"           />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
    </resultMap>

    <resultMap type="AdsClickRecord" id="AdsClickRecordWithRelatedResult" extends="AdsClickRecordResult">
        <result property="adsName"         column="ads_name"         />
        <result property="advertiserName"  column="advertiser_name"  />
        <result property="publisherName"   column="publisher_name"   />
    </resultMap>

    <sql id="selectAdsClickRecordVo">
        select record_id, click_id, offer_id, publisher_id, advertiser_id, ads_id, device_id, ip_address, user_agent, country_code, channel, sub_param1, sub_param2, sub_param3, target_url, payout, click_time, status, create_by, create_time, update_by, update_time, remark from ads_click_record
    </sql>

    <sql id="selectAdsClickRecordWithRelatedVo">
        select r.record_id, r.click_id, r.offer_id, r.publisher_id, r.advertiser_id, r.ads_id, r.device_id, r.ip_address, r.user_agent, r.country_code, r.channel, r.sub_param1, r.sub_param2, r.sub_param3, r.target_url, r.payout, r.click_time, r.status, r.create_by, r.create_time, r.update_by, r.update_time, r.remark,
               a.ads_name, adv.partner_name as advertiser_name, pub.partner_name as publisher_name
        from ads_click_record r
        left join ads_info a on r.ads_id = a.ads_id
        left join ads_partner adv on r.advertiser_id = adv.partner_id
        left join ads_partner pub on r.publisher_id = pub.partner_id
    </sql>

    <select id="selectAdsClickRecordList" parameterType="AdsClickRecord" resultMap="AdsClickRecordResult">
        <include refid="selectAdsClickRecordVo"/>
        <where>  
            <if test="clickId != null  and clickId != ''"> and click_id = #{clickId}</if>
            <if test="offerId != null "> and offer_id = #{offerId}</if>
            <if test="publisherId != null "> and publisher_id = #{publisherId}</if>
            <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
            <if test="adsId != null "> and ads_id = #{adsId}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            <if test="countryCode != null  and countryCode != ''"> and country_code = #{countryCode}</if>
            <if test="channel != null  and channel != ''"> and channel = #{channel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(click_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(click_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by click_time desc
    </select>

    <select id="selectAdsClickRecordListWithRelated" parameterType="AdsClickRecord" resultMap="AdsClickRecordWithRelatedResult">
        <include refid="selectAdsClickRecordWithRelatedVo"/>
        <where>  
            <if test="clickId != null  and clickId != ''"> and r.click_id = #{clickId}</if>
            <if test="offerId != null "> and r.offer_id = #{offerId}</if>
            <if test="publisherId != null "> and r.publisher_id = #{publisherId}</if>
            <if test="advertiserId != null "> and r.advertiser_id = #{advertiserId}</if>
            <if test="adsId != null "> and r.ads_id = #{adsId}</if>
            <if test="deviceId != null  and deviceId != ''"> and r.device_id = #{deviceId}</if>
            <if test="ipAddress != null  and ipAddress != ''"> and r.ip_address = #{ipAddress}</if>
            <if test="countryCode != null  and countryCode != ''"> and r.country_code = #{countryCode}</if>
            <if test="channel != null  and channel != ''"> and r.channel = #{channel}</if>
            <if test="status != null  and status != ''"> and r.status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(r.click_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(r.click_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by r.click_time desc
    </select>
    
    <select id="selectAdsClickRecordByRecordId" parameterType="Long" resultMap="AdsClickRecordResult">
        <include refid="selectAdsClickRecordVo"/>
        where record_id = #{recordId}
    </select>

    <select id="selectAdsClickRecordByClickId" parameterType="String" resultMap="AdsClickRecordResult">
        <include refid="selectAdsClickRecordVo"/>
        where click_id = #{clickId}
    </select>
        
    <insert id="insertAdsClickRecord" parameterType="AdsClickRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into ads_click_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clickId != null and clickId != ''">click_id,</if>
            <if test="offerId != null">offer_id,</if>
            <if test="publisherId != null">publisher_id,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="adsId != null">ads_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="countryCode != null">country_code,</if>
            <if test="channel != null">channel,</if>
            <if test="subParam1 != null">sub_param1,</if>
            <if test="subParam2 != null">sub_param2,</if>
            <if test="subParam3 != null">sub_param3,</if>
            <if test="targetUrl != null">target_url,</if>
            <if test="payout != null">payout,</if>
            <if test="clickTime != null">click_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clickId != null and clickId != ''">#{clickId},</if>
            <if test="offerId != null">#{offerId},</if>
            <if test="publisherId != null">#{publisherId},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="adsId != null">#{adsId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="countryCode != null">#{countryCode},</if>
            <if test="channel != null">#{channel},</if>
            <if test="subParam1 != null">#{subParam1},</if>
            <if test="subParam2 != null">#{subParam2},</if>
            <if test="subParam3 != null">#{subParam3},</if>
            <if test="targetUrl != null">#{targetUrl},</if>
            <if test="payout != null">#{payout},</if>
            <if test="clickTime != null">#{clickTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAdsClickRecord" parameterType="AdsClickRecord">
        update ads_click_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="clickId != null and clickId != ''">click_id = #{clickId},</if>
            <if test="offerId != null">offer_id = #{offerId},</if>
            <if test="publisherId != null">publisher_id = #{publisherId},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="adsId != null">ads_id = #{adsId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="subParam1 != null">sub_param1 = #{subParam1},</if>
            <if test="subParam2 != null">sub_param2 = #{subParam2},</if>
            <if test="subParam3 != null">sub_param3 = #{subParam3},</if>
            <if test="targetUrl != null">target_url = #{targetUrl},</if>
            <if test="payout != null">payout = #{payout},</if>
            <if test="clickTime != null">click_time = #{clickTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteAdsClickRecordByRecordId" parameterType="Long">
        delete from ads_click_record where record_id = #{recordId}
    </delete>

    <delete id="deleteAdsClickRecordByRecordIds" parameterType="String">
        delete from ads_click_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <!-- 其他查询方法 -->
    <select id="selectAdsClickRecordByOfferId" parameterType="Long" resultMap="AdsClickRecordResult">
        <include refid="selectAdsClickRecordVo"/>
        where offer_id = #{offerId}
        order by click_time desc
    </select>

    <select id="selectAdsClickRecordByPublisherId" parameterType="Long" resultMap="AdsClickRecordResult">
        <include refid="selectAdsClickRecordVo"/>
        where publisher_id = #{publisherId}
        order by click_time desc
    </select>

    <select id="selectAdsClickRecordByAdvertiserId" parameterType="Long" resultMap="AdsClickRecordResult">
        <include refid="selectAdsClickRecordVo"/>
        where advertiser_id = #{advertiserId}
        order by click_time desc
    </select>

    <select id="selectAdsClickRecordByAdsId" parameterType="Long" resultMap="AdsClickRecordResult">
        <include refid="selectAdsClickRecordVo"/>
        where ads_id = #{adsId}
        order by click_time desc
    </select>

    <select id="selectAdsClickRecordByTimeRange" resultMap="AdsClickRecordResult">
        <include refid="selectAdsClickRecordVo"/>
        <where>
            <if test="param1 != null">
                and click_time &gt;= #{param1}
            </if>
            <if test="param2 != null">
                and click_time &lt;= #{param2}
            </if>
        </where>
        order by click_time desc
    </select>

    <!-- 统计查询 -->
    <select id="countAdsClickRecord" resultType="Long">
        select count(*) from ads_click_record
    </select>

    <select id="countAdsClickRecordByCondition" parameterType="AdsClickRecord" resultType="Long">
        select count(*) from ads_click_record
        <where>  
            <if test="offerId != null "> and offer_id = #{offerId}</if>
            <if test="publisherId != null "> and publisher_id = #{publisherId}</if>
            <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
            <if test="adsId != null "> and ads_id = #{adsId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="countAdsClickRecordByTimeRange" resultType="Long">
        select count(*) from ads_click_record
        <where>
            <if test="param1 != null">
                and click_time &gt;= #{param1}
            </if>
            <if test="param2 != null">
                and click_time &lt;= #{param2}
            </if>
        </where>
    </select>

    <!-- 统计相关查询 -->

    <!-- 根据时间范围统计点击数 -->
    <select id="countByDateRange" resultType="java.lang.Long">
        select count(*)
        from ads_click_record
        <where>
            <if test="startTime != null">
                and click_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and click_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and publisher_id = #{publisherId}
            </if>
        </where>
    </select>

    <!-- 根据时间范围获取平均单价 -->
    <select id="getAvgPayoutByDateRange" resultType="java.lang.Double">
        select avg(payout)
        from ads_click_record
        <where>
            <if test="startTime != null">
                and click_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and click_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and publisher_id = #{publisherId}
            </if>
        </where>
    </select>

    <!-- 获取按广告主分布的点击统计 -->
    <select id="getClickDistributionByAdvertiser" resultType="java.util.Map">
        select
            r.advertiser_id as advertiserId,
            p.partner_name as advertiserName,
            count(*) as clickCount
        from ads_click_record r
        left join ads_partner p on r.advertiser_id = p.partner_id
        <where>
            <if test="startTime != null">
                and r.click_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and r.click_time &lt;= #{endTime}
            </if>
            <if test="publisherId != null">
                and r.publisher_id = #{publisherId}
            </if>
        </where>
        group by r.advertiser_id, p.partner_name
        order by clickCount desc
        limit 10
    </select>

    <!-- 获取按国家分布的点击统计 -->
    <select id="getClickDistributionByCountry" resultType="java.util.Map">
        select
            country_code as country,
            count(*) as clickCount
        from ads_click_record
        <where>
            <if test="startTime != null">
                and click_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and click_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and publisher_id = #{publisherId}
            </if>
        </where>
        group by country_code
        order by clickCount desc
        limit 10
    </select>

    <!-- 获取点击统计表格数据 -->
    <select id="getClickStatisticsTableData" resultType="java.util.Map">
        select
            DATE(r.click_time) as statisticsDate,
            r.publisher_id as publisherId,
            pub.partner_name as publisherName,
            r.advertiser_id as advertiserId,
            adv.partner_name as advertiserName,
            r.ads_id as adsId,
            a.ads_name as adsName,
            r.country_code as country,
            r.channel,
            count(*) as clickCount,
            sum(r.payout) as totalPayout,
            avg(r.payout) as avgPayout,
            count(distinct r.device_id) as uniqueDeviceCount,
            count(distinct r.ip_address) as uniqueIpCount
        from ads_click_record r
        left join ads_partner pub on r.publisher_id = pub.partner_id
        left join ads_partner adv on r.advertiser_id = adv.partner_id
        left join ads_info a on r.ads_id = a.ads_id
        <where>
            <if test="startTime != null">
                and r.click_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and r.click_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and r.advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and r.publisher_id = #{publisherId}
            </if>
        </where>
        group by DATE(r.click_time), r.publisher_id, r.advertiser_id, r.ads_id, r.country_code, r.channel
        order by statisticsDate desc, clickCount desc
    </select>

</mapper>
