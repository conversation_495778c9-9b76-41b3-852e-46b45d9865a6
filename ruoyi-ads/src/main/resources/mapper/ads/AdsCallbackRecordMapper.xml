<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ads.mapper.AdsCallbackRecordMapper">
    
    <resultMap type="AdsCallbackRecord" id="AdsCallbackRecordResult">
        <result property="recordId"         column="record_id"         />
        <result property="clickId"          column="click_id"          />
        <result property="callbackType"     column="callback_type"     />
        <result property="eventName"        column="event_name"        />
        <result property="eventValue"       column="event_value"       />
        <result property="offerId"          column="offer_id"          />
        <result property="publisherId"      column="publisher_id"      />
        <result property="advertiserId"     column="advertiser_id"     />
        <result property="adsId"            column="ads_id"            />
        <result property="callbackUrl"      column="callback_url"      />
        <result property="callbackStatus"   column="callback_status"   />
        <result property="callbackResponse" column="callback_response" />
        <result property="responseCode"     column="response_code"     />
        <result property="retryCount"       column="retry_count"       />
        <result property="callbackTime"     column="callback_time"     />
        <result property="status"           column="status"            />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
        <result property="remark"           column="remark"            />
    </resultMap>

    <resultMap type="AdsCallbackRecord" id="AdsCallbackRecordWithRelatedResult" extends="AdsCallbackRecordResult">
        <result property="adsName"         column="ads_name"         />
        <result property="advertiserName"  column="advertiser_name"  />
        <result property="publisherName"   column="publisher_name"   />
    </resultMap>

    <sql id="selectAdsCallbackRecordVo">
        select record_id, click_id, callback_type, event_name, event_value, offer_id, publisher_id, advertiser_id, ads_id, callback_url, callback_status, callback_response, response_code, retry_count, callback_time, status, create_by, create_time, update_by, update_time, remark from ads_callback_record
    </sql>

    <sql id="selectAdsCallbackRecordWithRelatedVo">
        select r.record_id, r.click_id, r.callback_type, r.event_name, r.event_value, r.offer_id, r.publisher_id, r.advertiser_id, r.ads_id, r.callback_url, r.callback_status, r.callback_response, r.response_code, r.retry_count, r.callback_time, r.status, r.create_by, r.create_time, r.update_by, r.update_time, r.remark,
               a.ads_name, adv.partner_name as advertiser_name, pub.partner_name as publisher_name
        from ads_callback_record r
        left join ads_info a on r.ads_id = a.ads_id
        left join ads_partner adv on r.advertiser_id = adv.partner_id
        left join ads_partner pub on r.publisher_id = pub.partner_id
    </sql>

    <select id="selectAdsCallbackRecordList" parameterType="AdsCallbackRecord" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        <where>  
            <if test="clickId != null  and clickId != ''"> and click_id = #{clickId}</if>
            <if test="callbackType != null  and callbackType != ''"> and callback_type = #{callbackType}</if>
            <if test="eventName != null  and eventName != ''"> and event_name = #{eventName}</if>
            <if test="offerId != null "> and offer_id = #{offerId}</if>
            <if test="publisherId != null "> and publisher_id = #{publisherId}</if>
            <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
            <if test="adsId != null "> and ads_id = #{adsId}</if>
            <if test="callbackStatus != null "> and callback_status = #{callbackStatus}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(callback_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(callback_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by callback_time desc
    </select>

    <select id="selectAdsCallbackRecordListWithRelated" parameterType="AdsCallbackRecord" resultMap="AdsCallbackRecordWithRelatedResult">
        <include refid="selectAdsCallbackRecordWithRelatedVo"/>
        <where>  
            <if test="clickId != null  and clickId != ''"> and r.click_id = #{clickId}</if>
            <if test="callbackType != null  and callbackType != ''"> and r.callback_type = #{callbackType}</if>
            <if test="eventName != null  and eventName != ''"> and r.event_name = #{eventName}</if>
            <if test="offerId != null "> and r.offer_id = #{offerId}</if>
            <if test="publisherId != null "> and r.publisher_id = #{publisherId}</if>
            <if test="advertiserId != null "> and r.advertiser_id = #{advertiserId}</if>
            <if test="adsId != null "> and r.ads_id = #{adsId}</if>
            <if test="callbackStatus != null "> and r.callback_status = #{callbackStatus}</if>
            <if test="status != null  and status != ''"> and r.status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(r.callback_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(r.callback_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by r.callback_time desc
    </select>
    
    <select id="selectAdsCallbackRecordByRecordId" parameterType="Long" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        where record_id = #{recordId}
    </select>
        
    <insert id="insertAdsCallbackRecord" parameterType="AdsCallbackRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into ads_callback_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clickId != null and clickId != ''">click_id,</if>
            <if test="callbackType != null and callbackType != ''">callback_type,</if>
            <if test="eventName != null">event_name,</if>
            <if test="eventValue != null">event_value,</if>
            <if test="offerId != null">offer_id,</if>
            <if test="publisherId != null">publisher_id,</if>
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="adsId != null">ads_id,</if>
            <if test="callbackUrl != null">callback_url,</if>
            <if test="callbackStatus != null">callback_status,</if>
            <if test="callbackResponse != null">callback_response,</if>
            <if test="responseCode != null">response_code,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="callbackTime != null">callback_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clickId != null and clickId != ''">#{clickId},</if>
            <if test="callbackType != null and callbackType != ''">#{callbackType},</if>
            <if test="eventName != null">#{eventName},</if>
            <if test="eventValue != null">#{eventValue},</if>
            <if test="offerId != null">#{offerId},</if>
            <if test="publisherId != null">#{publisherId},</if>
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="adsId != null">#{adsId},</if>
            <if test="callbackUrl != null">#{callbackUrl},</if>
            <if test="callbackStatus != null">#{callbackStatus},</if>
            <if test="callbackResponse != null">#{callbackResponse},</if>
            <if test="responseCode != null">#{responseCode},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="callbackTime != null">#{callbackTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAdsCallbackRecord" parameterType="AdsCallbackRecord">
        update ads_callback_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="clickId != null and clickId != ''">click_id = #{clickId},</if>
            <if test="callbackType != null and callbackType != ''">callback_type = #{callbackType},</if>
            <if test="eventName != null">event_name = #{eventName},</if>
            <if test="eventValue != null">event_value = #{eventValue},</if>
            <if test="offerId != null">offer_id = #{offerId},</if>
            <if test="publisherId != null">publisher_id = #{publisherId},</if>
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="adsId != null">ads_id = #{adsId},</if>
            <if test="callbackUrl != null">callback_url = #{callbackUrl},</if>
            <if test="callbackStatus != null">callback_status = #{callbackStatus},</if>
            <if test="callbackResponse != null">callback_response = #{callbackResponse},</if>
            <if test="responseCode != null">response_code = #{responseCode},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="callbackTime != null">callback_time = #{callbackTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteAdsCallbackRecordByRecordId" parameterType="Long">
        delete from ads_callback_record where record_id = #{recordId}
    </delete>

    <delete id="deleteAdsCallbackRecordByRecordIds" parameterType="String">
        delete from ads_callback_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>

    <!-- 其他查询方法 -->
    <select id="selectAdsCallbackRecordByClickId" parameterType="String" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        where click_id = #{clickId}
        order by callback_time desc
    </select>

    <select id="selectAdsCallbackRecordByCallbackType" parameterType="String" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        where callback_type = #{callbackType}
        order by callback_time desc
    </select>

    <select id="selectAdsCallbackRecordByCallbackStatus" parameterType="Integer" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        where callback_status = #{callbackStatus}
        order by callback_time desc
    </select>

    <select id="selectAdsCallbackRecordByPublisherId" parameterType="Long" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        where publisher_id = #{publisherId}
        order by callback_time desc
    </select>

    <select id="selectAdsCallbackRecordByAdvertiserId" parameterType="Long" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        where advertiser_id = #{advertiserId}
        order by callback_time desc
    </select>

    <select id="selectAdsCallbackRecordByAdsId" parameterType="Long" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        where ads_id = #{adsId}
        order by callback_time desc
    </select>

    <select id="selectAdsCallbackRecordByTimeRange" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        <where>
            <if test="param1 != null">
                and callback_time &gt;= #{param1}
            </if>
            <if test="param2 != null">
                and callback_time &lt;= #{param2}
            </if>
        </where>
        order by callback_time desc
    </select>

    <select id="selectRetryableCallbackRecords" resultMap="AdsCallbackRecordResult">
        <include refid="selectAdsCallbackRecordVo"/>
        where callback_status = 2 and retry_count &lt; 3
        order by callback_time asc
    </select>

    <!-- 统计查询 -->
    <select id="countAdsCallbackRecord" resultType="Long">
        select count(*) from ads_callback_record
    </select>

    <select id="countAdsCallbackRecordByCondition" parameterType="AdsCallbackRecord" resultType="Long">
        select count(*) from ads_callback_record
        <where>  
            <if test="callbackType != null  and callbackType != ''"> and callback_type = #{callbackType}</if>
            <if test="offerId != null "> and offer_id = #{offerId}</if>
            <if test="publisherId != null "> and publisher_id = #{publisherId}</if>
            <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
            <if test="adsId != null "> and ads_id = #{adsId}</if>
            <if test="callbackStatus != null "> and callback_status = #{callbackStatus}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="countAdsCallbackRecordByCallbackStatus" parameterType="Integer" resultType="Long">
        select count(*) from ads_callback_record where callback_status = #{callbackStatus}
    </select>

    <select id="countAdsCallbackRecordByTimeRange" resultType="Long">
        select count(*) from ads_callback_record
        <where>
            <if test="param1 != null">
                and callback_time &gt;= #{param1}
            </if>
            <if test="param2 != null">
                and callback_time &lt;= #{param2}
            </if>
        </where>
    </select>

    <!-- 统计相关查询 -->

    <!-- 根据时间范围统计回调数 -->
    <select id="countByDateRange" resultType="java.lang.Long">
        select count(*)
        from ads_callback_record
        <where>
            <if test="startTime != null">
                and callback_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and callback_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and publisher_id = #{publisherId}
            </if>
            <if test="callbackType != null and callbackType != ''">
                and callback_type = #{callbackType}
            </if>
        </where>
    </select>

    <!-- 根据状态和时间范围统计回调数 -->
    <select id="countByStatusAndDateRange" resultType="java.lang.Long">
        select count(*)
        from ads_callback_record
        <where>
            and callback_status = #{status}
            <if test="startTime != null">
                and callback_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and callback_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and publisher_id = #{publisherId}
            </if>
            <if test="callbackType != null and callbackType != ''">
                and callback_type = #{callbackType}
            </if>
        </where>
    </select>

    <!-- 获取按回调类型分布的统计 -->
    <select id="getCallbackDistributionByType" resultType="java.util.Map">
        select
            callback_type as callbackType,
            count(*) as callbackCount
        from ads_callback_record
        <where>
            <if test="startTime != null">
                and callback_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and callback_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and publisher_id = #{publisherId}
            </if>
        </where>
        group by callback_type
        order by callbackCount desc
    </select>

    <!-- 获取按状态分布的统计 -->
    <select id="getCallbackDistributionByStatus" resultType="java.util.Map">
        select
            callback_status as status,
            count(*) as callbackCount
        from ads_callback_record
        <where>
            <if test="startTime != null">
                and callback_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and callback_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and publisher_id = #{publisherId}
            </if>
            <if test="callbackType != null and callbackType != ''">
                and callback_type = #{callbackType}
            </if>
        </where>
        group by callback_status
        order by callbackCount desc
    </select>

    <!-- 获取回调统计表格数据 -->
    <select id="getCallbackStatisticsTableData" resultType="java.util.Map">
        select
            DATE(r.callback_time) as statisticsDate,
            r.publisher_id as publisherId,
            pub.partner_name as publisherName,
            r.advertiser_id as advertiserId,
            adv.partner_name as advertiserName,
            r.ads_id as adsId,
            a.ads_name as adsName,
            r.callback_type as callbackType,
            count(*) as totalCallbacks,
            sum(case when r.callback_status = 1 then 1 else 0 end) as successCallbacks,
            sum(case when r.callback_status = 2 then 1 else 0 end) as failedCallbacks,
            sum(case when r.callback_status = 0 then 1 else 0 end) as pendingCallbacks,
            avg(r.retry_count) as avgRetryCount
        from ads_callback_record r
        left join ads_partner pub on r.publisher_id = pub.partner_id
        left join ads_partner adv on r.advertiser_id = adv.partner_id
        left join ads_info a on r.ads_id = a.ads_id
        <where>
            <if test="startTime != null">
                and r.callback_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and r.callback_time &lt;= #{endTime}
            </if>
            <if test="advertiserId != null">
                and r.advertiser_id = #{advertiserId}
            </if>
            <if test="publisherId != null">
                and r.publisher_id = #{publisherId}
            </if>
            <if test="callbackType != null and callbackType != ''">
                and r.callback_type = #{callbackType}
            </if>
        </where>
        group by DATE(r.callback_time), r.publisher_id, r.advertiser_id, r.ads_id, r.callback_type
        order by statisticsDate desc, totalCallbacks desc
    </select>

</mapper>
