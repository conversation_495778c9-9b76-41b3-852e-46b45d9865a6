<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ads.mapper.AdsPartnerMapper">
    
    <resultMap type="AdsPartner" id="AdsPartnerResult">
        <result property="partnerId"           column="partner_id"           />
        <result property="partnerKey"          column="partner_key"          />
        <result property="partnerName"         column="partner_name"         />
        <result property="partnerType"         column="partner_type"         />
        <result property="contactPerson"       column="contact_person"       />
        <result property="contactEmail"        column="contact_email"        />
        <result property="contactPhone"        column="contact_phone"        />
        <result property="installPostbackUrl"  column="install_postback_url" />
        <result property="eventPostbackUrl"    column="event_postback_url"   />
        <result property="enableCallback"      column="enable_callback"      />
        <result property="status"              column="status"               />
        <result property="delFlag"             column="del_flag"             />
        <result property="createBy"            column="create_by"            />
        <result property="createTime"          column="create_time"          />
        <result property="updateBy"            column="update_by"            />
        <result property="updateTime"          column="update_time"          />
        <result property="remark"              column="remark"               />
    </resultMap>

    <sql id="selectAdsPartnerVo">
        select partner_id, partner_key, partner_name, partner_type, contact_person, contact_email, contact_phone, install_postback_url, event_postback_url, enable_callback, status, del_flag, create_by, create_time, update_by, update_time, remark from ads_partner
    </sql>

    <select id="selectAdsPartnerList" parameterType="AdsPartner" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        <where>  
            <if test="partnerKey != null  and partnerKey != ''"> and partner_key = #{partnerKey}</if>
            <if test="partnerName != null  and partnerName != ''"> and partner_name like concat('%', #{partnerName}, '%')</if>
            <if test="partnerType != null"> and partner_type = #{partnerType}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactEmail != null  and contactEmail != ''"> and contact_email like concat('%', #{contactEmail}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            and del_flag = '0'
        </where>

        order by create_time desc
    </select>
    
    <select id="selectAdsPartnerByPartnerId" parameterType="Long" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        where partner_id = #{partnerId} and del_flag = '0'
    </select>
        
    <insert id="insertAdsPartner" parameterType="AdsPartner" useGeneratedKeys="true" keyProperty="partnerId">
        insert into ads_partner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="partnerKey != null and partnerKey != ''">partner_key,</if>
            <if test="partnerName != null">partner_name,</if>
            <if test="partnerType != null">partner_type,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="installPostbackUrl != null">install_postback_url,</if>
            <if test="eventPostbackUrl != null">event_postback_url,</if>
            <if test="enableCallback != null">enable_callback,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="partnerKey != null and partnerKey != ''">#{partnerKey},</if>
            <if test="partnerName != null">#{partnerName},</if>
            <if test="partnerType != null">#{partnerType},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="installPostbackUrl != null">#{installPostbackUrl},</if>
            <if test="eventPostbackUrl != null">#{eventPostbackUrl},</if>
            <if test="enableCallback != null">#{enableCallback},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAdsPartner" parameterType="AdsPartner">
        update ads_partner
        <trim prefix="SET" suffixOverrides=",">
            <if test="partnerKey != null and partnerKey != ''">partner_key = #{partnerKey},</if>
            <if test="partnerName != null">partner_name = #{partnerName},</if>
            <if test="partnerType != null">partner_type = #{partnerType},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="installPostbackUrl != null">install_postback_url = #{installPostbackUrl},</if>
            <if test="eventPostbackUrl != null">event_postback_url = #{eventPostbackUrl},</if>
            <if test="enableCallback != null">enable_callback = #{enableCallback},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where partner_id = #{partnerId}
    </update>

    <delete id="deleteAdsPartnerByPartnerId" parameterType="Long">
        update ads_partner set del_flag = '2' where partner_id = #{partnerId}
    </delete>

    <delete id="deleteAdsPartnerByPartnerIds" parameterType="String">
        update ads_partner set del_flag = '2' where partner_id in 
        <foreach item="partnerId" collection="array" open="(" separator="," close=")">
            #{partnerId}
        </foreach>
    </delete>

    <select id="selectAdsPartnerByPartnerKey" parameterType="String" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        where partner_key = #{partnerKey} and del_flag = '0'
    </select>

    <select id="selectAdsPartnerByPartnerType" parameterType="Integer" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        where partner_type = #{partnerType} and del_flag = '0' and status = '0'
        order by create_time desc
    </select>

    <select id="selectAdsPartnerByStatus" parameterType="String" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        where status = #{status} and del_flag = '0'
        order by create_time desc
    </select>

    <select id="checkPartnerKeyUnique" parameterType="AdsPartner" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        where partner_key = #{partnerKey} and del_flag = '0'
        <if test="partnerId != null and partnerId != 0">
            and partner_id != #{partnerId}
        </if>
        limit 1
    </select>

    <select id="checkPartnerNameUnique" parameterType="AdsPartner" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        where partner_name = #{partnerName} and del_flag = '0'
        <if test="partnerId != null and partnerId != 0">
            and partner_id != #{partnerId}
        </if>
        limit 1
    </select>

    <select id="selectAdvertiserList" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        where partner_type = 0 and del_flag = '0' and status = '0'
        order by create_time desc
    </select>

    <select id="selectPublisherList" resultMap="AdsPartnerResult">
        <include refid="selectAdsPartnerVo"/>
        where partner_type = 1 and del_flag = '0' and status = '0'
        order by create_time desc
    </select>

</mapper>
