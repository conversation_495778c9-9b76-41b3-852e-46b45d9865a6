<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ads.mapper.AdsInfoMapper">
    
    <resultMap type="AdsInfo" id="AdsInfoResult">
        <result property="adsId"           column="ads_id"           />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="adsName"         column="ads_name"         />
        <result property="appId"           column="app_id"           />
        <result property="packageName"     column="package_name"     />
        <result property="appName"         column="app_name"         />
        <result property="iconUrl"         column="icon_url"         />
        <result property="description"     column="description"      />
        <result property="clickUrl"        column="click_url"        />
        <result property="previewUrl"      column="preview_url"      />
        <result property="country"         column="country"          />
        <result property="category"        column="category"         />
        <result property="clickCap"        column="click_cap"        />
        <result property="installCap"      column="install_cap"      />
        <result property="conversionCap"   column="conversion_cap"   />
        <result property="status"          column="status"           />
        <result property="kpi"             column="kpi"              />
        <result property="conversionEvent" column="conversion_event" />
        <result property="delFlag"         column="del_flag"         />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
    </resultMap>

    <sql id="selectAdsInfoVo">
        select ads_id, advertiser_id, ads_name, app_id, package_name, app_name, icon_url, description, click_url, preview_url, country, category, click_cap, install_cap, conversion_cap, status, kpi, conversion_event, del_flag, create_by, create_time, update_by, update_time, remark from ads_info
    </sql>

    <select id="selectAdsInfoList" parameterType="AdsInfo" resultMap="AdsInfoResult">
        <include refid="selectAdsInfoVo"/>
        <where>  
            <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
            <if test="adsName != null  and adsName != ''"> and ads_name like concat('%', #{adsName}, '%')</if>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="packageName != null  and packageName != ''"> and package_name like concat('%', #{packageName}, '%')</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
            <if test="country != null  and country != ''"> and country like concat('%', #{country}, '%')</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            and del_flag = '0'
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAdsInfoByAdsId" parameterType="Long" resultMap="AdsInfoResult">
        <include refid="selectAdsInfoVo"/>
        where ads_id = #{adsId} and del_flag = '0'
    </select>
        
    <insert id="insertAdsInfo" parameterType="AdsInfo" useGeneratedKeys="true" keyProperty="adsId">
        insert into ads_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="adsName != null and adsName != ''">ads_name,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="packageName != null">package_name,</if>
            <if test="appName != null">app_name,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="description != null">description,</if>
            <if test="clickUrl != null">click_url,</if>
            <if test="previewUrl != null">preview_url,</if>
            <if test="country != null">country,</if>
            <if test="category != null">category,</if>
            <if test="clickCap != null">click_cap,</if>
            <if test="installCap != null">install_cap,</if>
            <if test="conversionCap != null">conversion_cap,</if>
            <if test="status != null">status,</if>
            <if test="kpi != null">kpi,</if>
            <if test="conversionEvent != null">conversion_event,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="adsName != null and adsName != ''">#{adsName},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="packageName != null">#{packageName},</if>
            <if test="appName != null">#{appName},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="description != null">#{description},</if>
            <if test="clickUrl != null">#{clickUrl},</if>
            <if test="previewUrl != null">#{previewUrl},</if>
            <if test="country != null">#{country},</if>
            <if test="category != null">#{category},</if>
            <if test="clickCap != null">#{clickCap},</if>
            <if test="installCap != null">#{installCap},</if>
            <if test="conversionCap != null">#{conversionCap},</if>
            <if test="status != null">#{status},</if>
            <if test="kpi != null">#{kpi},</if>
            <if test="conversionEvent != null">#{conversionEvent},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAdsInfo" parameterType="AdsInfo">
        update ads_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="adsName != null and adsName != ''">ads_name = #{adsName},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="packageName != null">package_name = #{packageName},</if>
            <if test="appName != null">app_name = #{appName},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="description != null">description = #{description},</if>
            <if test="clickUrl != null">click_url = #{clickUrl},</if>
            <if test="previewUrl != null">preview_url = #{previewUrl},</if>
            <if test="country != null">country = #{country},</if>
            <if test="category != null">category = #{category},</if>
            <if test="clickCap != null">click_cap = #{clickCap},</if>
            <if test="installCap != null">install_cap = #{installCap},</if>
            <if test="conversionCap != null">conversion_cap = #{conversionCap},</if>
            <if test="status != null">status = #{status},</if>
            <if test="kpi != null">kpi = #{kpi},</if>
            <if test="conversionEvent != null">conversion_event = #{conversionEvent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where ads_id = #{adsId}
    </update>

    <delete id="deleteAdsInfoByAdsId" parameterType="Long">
        update ads_info set del_flag = '2' where ads_id = #{adsId}
    </delete>

    <delete id="deleteAdsInfoByAdsIds" parameterType="String">
        update ads_info set del_flag = '2' where ads_id in 
        <foreach item="adsId" collection="array" open="(" separator="," close=")">
            #{adsId}
        </foreach>
    </delete>

    <select id="selectAdsInfoByAdvertiserId" parameterType="Long" resultMap="AdsInfoResult">
        <include refid="selectAdsInfoVo"/>
        where advertiser_id = #{advertiserId} and del_flag = '0' and status = '0'
        order by create_time desc
    </select>

    <select id="selectAdsInfoByStatus" parameterType="String" resultMap="AdsInfoResult">
        <include refid="selectAdsInfoVo"/>
        where status = #{status} and del_flag = '0'
        order by create_time desc
    </select>

    <select id="selectAdsInfoByAppId" parameterType="String" resultMap="AdsInfoResult">
        <include refid="selectAdsInfoVo"/>
        where app_id = #{appId} and del_flag = '0'
    </select>

    <select id="checkAdsNameUnique" parameterType="AdsInfo" resultMap="AdsInfoResult">
        <include refid="selectAdsInfoVo"/>
        where ads_name = #{adsName} and del_flag = '0'
        <if test="adsId != null and adsId != 0">
            and ads_id != #{adsId}
        </if>
        limit 1
    </select>

    <select id="checkAppIdUnique" parameterType="AdsInfo" resultMap="AdsInfoResult">
        <include refid="selectAdsInfoVo"/>
        where app_id = #{appId} and del_flag = '0'
        <if test="adsId != null and adsId != 0">
            and ads_id != #{adsId}
        </if>
        limit 1
    </select>

</mapper>
