<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ads.mapper.AdsPublisherOfferMapper">
    
    <resultMap type="AdsPublisherOffer" id="AdsPublisherOfferResult">
        <result property="offerId"         column="offer_id"         />
        <result property="advertiserId"    column="advertiser_id"    />
        <result property="publisherId"     column="publisher_id"     />
        <result property="adsId"           column="ads_id"           />
        <result property="payout"          column="payout"           />
        <result property="country"         column="country"          />
        <result property="installCap"      column="install_cap"      />
        <result property="dailyCap"        column="daily_cap"        />
        <result property="clickUrl"        column="click_url"        />
        <result property="previewUrl"      column="preview_url"      />
        <result property="priority"        column="priority"         />
        <result property="status"          column="status"           />
        <result property="kpi"             column="kpi"              />
        <result property="delFlag"         column="del_flag"         />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
    </resultMap>

    <resultMap type="AdsPublisherOffer" id="AdsPublisherOfferWithRelatedResult" extends="AdsPublisherOfferResult">
        <result property="adsName"         column="ads_name"         />
        <result property="advertiserName"  column="advertiser_name"  />
        <result property="publisherName"   column="publisher_name"   />
    </resultMap>

    <sql id="selectAdsPublisherOfferVo">
        select offer_id, advertiser_id, publisher_id, ads_id, payout, country, install_cap, daily_cap, click_url, preview_url, priority, status, kpi, del_flag, create_by, create_time, update_by, update_time, remark from ads_publisher_offer
    </sql>

    <sql id="selectAdsPublisherOfferWithRelatedVo">
        select o.offer_id, o.advertiser_id, o.publisher_id, o.ads_id, o.payout, o.country, o.install_cap, o.daily_cap, o.click_url, o.preview_url, o.priority, o.status,o.kpi, o.del_flag, o.create_by, o.create_time, o.update_by, o.update_time, o.remark,
               a.ads_name, adv.partner_name as advertiser_name, pub.partner_name as publisher_name
        from ads_publisher_offer o
        left join ads_info a on o.ads_id = a.ads_id
        left join ads_partner adv on o.advertiser_id = adv.partner_id
        left join ads_partner pub on o.publisher_id = pub.partner_id
    </sql>

    <select id="selectAdsPublisherOfferList" parameterType="AdsPublisherOffer" resultMap="AdsPublisherOfferResult">
        <include refid="selectAdsPublisherOfferVo"/>
        <where>  
            <if test="advertiserId != null "> and advertiser_id = #{advertiserId}</if>
            <if test="publisherId != null "> and publisher_id = #{publisherId}</if>
            <if test="adsId != null "> and ads_id = #{adsId}</if>
            <if test="country != null  and country != ''"> and country like concat('%', #{country}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            and del_flag = '0'
        </where>

        order by priority asc, create_time desc
    </select>

    <select id="selectAdsPublisherOfferListWithRelated" parameterType="AdsPublisherOffer" resultMap="AdsPublisherOfferWithRelatedResult">
        <include refid="selectAdsPublisherOfferWithRelatedVo"/>
        <where>  
            <if test="advertiserId != null "> and o.advertiser_id = #{advertiserId}</if>
            <if test="publisherId != null "> and o.publisher_id = #{publisherId}</if>
            <if test="adsId != null "> and o.ads_id = #{adsId}</if>
            <if test="country != null  and country != ''"> and o.country like concat('%', #{country}, '%')</if>
            <if test="status != null  and status != ''"> and o.status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(o.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(o.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            and o.del_flag = '0'
        </where>

        order by o.priority asc, o.create_time desc
    </select>
    
    <select id="selectAdsPublisherOfferByOfferId" parameterType="Long" resultMap="AdsPublisherOfferResult">
        <include refid="selectAdsPublisherOfferVo"/>
        where offer_id = #{offerId} and del_flag = '0'
    </select>

    <select id="selectAdsPublisherOfferWithRelatedByOfferId" parameterType="Long" resultMap="AdsPublisherOfferWithRelatedResult">
        <include refid="selectAdsPublisherOfferWithRelatedVo"/>
        where o.offer_id = #{offerId} and o.del_flag = '0'
    </select>
        
    <insert id="insertAdsPublisherOffer" parameterType="AdsPublisherOffer" useGeneratedKeys="true" keyProperty="offerId">
        insert into ads_publisher_offer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">advertiser_id,</if>
            <if test="publisherId != null">publisher_id,</if>
            <if test="adsId != null">ads_id,</if>
            <if test="payout != null">payout,</if>
            <if test="country != null and country != ''">country,</if>
            <if test="installCap != null">install_cap,</if>
            <if test="dailyCap != null">daily_cap,</if>
            <if test="clickUrl != null and clickUrl != ''">click_url,</if>
            <if test="previewUrl != null">preview_url,</if>
            <if test="priority != null">priority,</if>
            <if test="status != null">status,</if>
            <if test="kpi != null">kpi,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="advertiserId != null">#{advertiserId},</if>
            <if test="publisherId != null">#{publisherId},</if>
            <if test="adsId != null">#{adsId},</if>
            <if test="payout != null">#{payout},</if>
            <if test="country != null and country != ''">#{country},</if>
            <if test="installCap != null">#{installCap},</if>
            <if test="dailyCap != null">#{dailyCap},</if>
            <if test="clickUrl != null and clickUrl != ''">#{clickUrl},</if>
            <if test="previewUrl != null">#{previewUrl},</if>
            <if test="priority != null">#{priority},</if>
            <if test="status != null">#{status},</if>
            <if test="kpi != null">#{kpi},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAdsPublisherOffer" parameterType="AdsPublisherOffer">
        update ads_publisher_offer
        <trim prefix="SET" suffixOverrides=",">
            <if test="advertiserId != null">advertiser_id = #{advertiserId},</if>
            <if test="publisherId != null">publisher_id = #{publisherId},</if>
            <if test="adsId != null">ads_id = #{adsId},</if>
            <if test="payout != null">payout = #{payout},</if>
            <if test="country != null and country != ''">country = #{country},</if>
            <if test="installCap != null">install_cap = #{installCap},</if>
            <if test="dailyCap != null">daily_cap = #{dailyCap},</if>
            <if test="clickUrl != null and clickUrl != ''">click_url = #{clickUrl},</if>
            <if test="previewUrl != null">preview_url = #{previewUrl},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="status != null">status = #{status},</if>
            <if test="kpi != null">kpi = #{kpi},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where offer_id = #{offerId}
    </update>

    <delete id="deleteAdsPublisherOfferByOfferId" parameterType="Long">
        update ads_publisher_offer set del_flag = '2' where offer_id = #{offerId}
    </delete>

    <delete id="deleteAdsPublisherOfferByOfferIds" parameterType="String">
        update ads_publisher_offer set del_flag = '2' where offer_id in 
        <foreach item="offerId" collection="array" open="(" separator="," close=")">
            #{offerId}
        </foreach>
    </delete>

    <select id="selectAdsPublisherOfferByAdsIdAndPublisherId" resultMap="AdsPublisherOfferResult">
        <include refid="selectAdsPublisherOfferVo"/>
        where ads_id = #{adsId} and publisher_id = #{publisherId} and del_flag = '0'
    </select>

    <select id="selectAdsPublisherOfferByAdvertiserId" parameterType="Long" resultMap="AdsPublisherOfferResult">
        <include refid="selectAdsPublisherOfferVo"/>
        where advertiser_id = #{advertiserId} and del_flag = '0' and status = '0'
        order by priority asc, create_time desc
    </select>

    <select id="selectAdsPublisherOfferByPublisherId" parameterType="Long" resultMap="AdsPublisherOfferResult">
        <include refid="selectAdsPublisherOfferVo"/>
        where publisher_id = #{publisherId} and del_flag = '0' and status = '0'
        order by priority asc, create_time desc
    </select>

    <select id="selectAdsPublisherOfferByAdsId" parameterType="Long" resultMap="AdsPublisherOfferResult">
        <include refid="selectAdsPublisherOfferVo"/>
        where ads_id = #{adsId} and del_flag = '0' and status = '0'
        order by priority asc, create_time desc
    </select>

    <select id="selectAdsPublisherOfferByStatus" parameterType="String" resultMap="AdsPublisherOfferResult">
        <include refid="selectAdsPublisherOfferVo"/>
        where status = #{status} and del_flag = '0'
        order by priority asc, create_time desc
    </select>

    <select id="checkAdsPublisherUnique" parameterType="AdsPublisherOffer" resultMap="AdsPublisherOfferResult">
        <include refid="selectAdsPublisherOfferVo"/>
        where ads_id = #{adsId} and publisher_id = #{publisherId} and del_flag = '0'
        <if test="offerId != null and offerId != 0">
            and offer_id != #{offerId}
        </if>
        limit 1
    </select>

</mapper>
