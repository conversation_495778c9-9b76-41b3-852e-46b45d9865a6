package com.ruoyi.ads.exception;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.ads.domain.ApiResponse;

/**
 * API异常处理器
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestControllerAdvice(basePackages = "com.ruoyi.ads.controller")
public class ApiExceptionHandler
{
    private static final Logger log = LoggerFactory.getLogger(ApiExceptionHandler.class);

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleServiceException(ServiceException e, HttpServletRequest request)
    {
        log.error("业务异常：{}", e.getMessage());
        return ApiResponse.badRequest(e.getMessage()).requestId(getRequestId(request));
    }

    /**
     * 参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleValidationException(MethodArgumentNotValidException e, HttpServletRequest request)
    {
        log.error("参数验证异常：{}", e.getMessage());
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return ApiResponse.badRequest("参数验证失败：" + message).requestId(getRequestId(request));
    }

    /**
     * 参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleBindException(BindException e, HttpServletRequest request)
    {
        log.error("参数绑定异常：{}", e.getMessage());
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return ApiResponse.badRequest("参数绑定失败：" + message).requestId(getRequestId(request));
    }

    /**
     * 约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request)
    {
        log.error("约束违反异常：{}", e.getMessage());
        return ApiResponse.badRequest("参数约束违反：" + e.getMessage()).requestId(getRequestId(request));
    }

    /**
     * 缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleMissingParameterException(MissingServletRequestParameterException e, HttpServletRequest request)
    {
        log.error("缺少请求参数异常：{}", e.getMessage());
        return ApiResponse.badRequest("缺少必需参数：" + e.getParameterName()).requestId(getRequestId(request));
    }

    /**
     * 参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request)
    {
        log.error("参数类型不匹配异常：{}", e.getMessage());
        return ApiResponse.badRequest("参数类型错误：" + e.getName()).requestId(getRequestId(request));
    }

    /**
     * 参数错误异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Object> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        String requestId = getRequestId(request);
        log.error("参数错误异常 - URI: {}, RequestId: {}, Error: {}", requestURI, requestId, e.getMessage());

        // 为点击追踪API提供更详细的错误信息
        if (requestURI.contains("/api/click")) {
            return ApiResponse.badRequest("点击追踪失败: " + e.getMessage()).requestId(requestId);
        }

        return ApiResponse.badRequest(e.getMessage()).requestId(requestId);
    }

    /**
     * 安全异常
     */
    @ExceptionHandler(SecurityException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ApiResponse<Object> handleSecurityException(SecurityException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        String requestId = getRequestId(request);
        log.error("安全异常 - URI: {}, RequestId: {}, Error: {}", requestURI, requestId, e.getMessage());

        // 为点击追踪API提供更详细的错误信息
        if (requestURI.contains("/api/click")) {
            return ApiResponse.forbidden("点击追踪权限验证失败: " + e.getMessage()).requestId(requestId);
        }

        return ApiResponse.forbidden(e.getMessage()).requestId(requestId);
    }

    /**
     * 请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ApiResponse<Object> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request)
    {
        log.error("请求方法不支持异常：{}", e.getMessage());
        return ApiResponse.methodNotAllowed("不支持的请求方法：" + e.getMethod()).requestId(getRequestId(request));
    }

    /**
     * 运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Object> handleRuntimeException(RuntimeException e, HttpServletRequest request)
    {
        log.error("运行时异常：", e);
        return ApiResponse.error("系统内部错误").requestId(getRequestId(request));
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Object> handleException(Exception e, HttpServletRequest request)
    {
        log.error("系统异常：", e);
        return ApiResponse.error("系统异常，请联系管理员").requestId(getRequestId(request));
    }

    /**
     * 获取请求ID
     */
    private String getRequestId(HttpServletRequest request)
    {
        String requestId = request.getHeader("X-Request-ID");
        if (requestId == null || requestId.isEmpty())
        {
            requestId = java.util.UUID.randomUUID().toString().replace("-", "");
        }
        return requestId;
    }
}
