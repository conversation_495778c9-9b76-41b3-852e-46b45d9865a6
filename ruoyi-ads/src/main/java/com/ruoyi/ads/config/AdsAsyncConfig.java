package com.ruoyi.ads.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 广告模块异步处理配置
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Configuration
@EnableAsync
public class AdsAsyncConfig
{
    private static final Logger logger = LoggerFactory.getLogger(AdsAsyncConfig.class);

    /**
     * 点击追踪异步处理线程池
     */
    @Bean("adsClickExecutor")
    public Executor adsClickExecutor()
    {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("ads-click-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        // 设置异常处理器
        executor.setTaskDecorator(runnable -> () -> {
            try {
                runnable.run();
            } catch (Exception e) {
                logger.error("异步任务执行失败", e);
            }
        });
        
        executor.initialize();
        logger.info("点击追踪异步线程池初始化完成");
        return executor;
    }

    /**
     * 回调处理异步线程池
     */
    @Bean("adsCallbackExecutor")
    public Executor adsCallbackExecutor()
    {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(50);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("ads-callback-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        // 设置异常处理器
        executor.setTaskDecorator(runnable -> () -> {
            try {
                runnable.run();
            } catch (Exception e) {
                logger.error("回调异步任务执行失败", e);
            }
        });
        
        executor.initialize();
        logger.info("回调处理异步线程池初始化完成");
        return executor;
    }
}
