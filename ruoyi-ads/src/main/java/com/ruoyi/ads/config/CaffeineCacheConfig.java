package com.ruoyi.ads.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * Caffeine缓存配置
 * 用于广告系统的高频数据缓存
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Configuration
@EnableCaching
public class CaffeineCacheConfig
{
    /**
     * Caffeine缓存管理器
     * 用于广告系统的高频数据缓存（合作伙伴、广告信息等）
     */
    @Bean("caffeineCacheManager")
    @Primary
    public CacheManager caffeineCacheManager()
    {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // 配置Caffeine缓存
        cacheManager.setCaffeine(Caffeine.newBuilder()
                // 设置最大缓存数量
                .maximumSize(10000)
                // 设置写入后过期时间
                .expireAfterWrite(30, TimeUnit.MINUTES)
                // 设置访问后过期时间
                .expireAfterAccess(10, TimeUnit.MINUTES)
                // 设置初始容量
                .initialCapacity(100)
                // 开启统计
                .recordStats());
        
        // 设置缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
                "adsPartnerCache",           // 合作伙伴缓存
                "adsInfoCache",              // 广告信息缓存
                "adsPublisherOfferCache",    // 发布者配置缓存
                "adsAdvertiserOfferCache"    // 广告主配置缓存
        ));
        
        return cacheManager;
    }
    
    /**
     * 长期缓存管理器
     * 用于不经常变化的数据
     */
    @Bean("longTermCacheManager")
    public CacheManager longTermCacheManager()
    {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(5000)
                // 长期缓存，2小时过期
                .expireAfterWrite(2, TimeUnit.HOURS)
                .expireAfterAccess(1, TimeUnit.HOURS)
                .initialCapacity(50)
                .recordStats());
        
        cacheManager.setCacheNames(java.util.Arrays.asList(
                "adsConfigCache",            // 系统配置缓存
                "adsStaticDataCache"         // 静态数据缓存
        ));
        
        return cacheManager;
    }
    
    /**
     * 短期缓存管理器
     * 用于频繁变化的数据
     */
    @Bean("shortTermCacheManager")
    public CacheManager shortTermCacheManager()
    {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(20000)
                // 短期缓存，5分钟过期
                .expireAfterWrite(5, TimeUnit.MINUTES)
                .expireAfterAccess(2, TimeUnit.MINUTES)
                .initialCapacity(200)
                .recordStats());
        
        cacheManager.setCacheNames(java.util.Arrays.asList(
                "adsClickCache",             // 点击相关缓存
                "adsCallbackCache"           // 回调相关缓存
        ));
        
        return cacheManager;
    }
}
