package com.ruoyi.ads.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 广告错误处理配置
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Configuration
public class AdsErrorConfig
{
    @Value("${ruoyi.profile:prod}")
    private String profile;

    @Value("${ads.error.debug:false}")
    private boolean debugMode;

    @Value("${ads.error.redirect:true}")
    private boolean redirectToErrorPage;

    /**
     * 是否为调试模式
     */
    public boolean isDebugMode()
    {
        return debugMode || "dev".equals(profile) || "test".equals(profile);
    }

    /**
     * 是否重定向到错误页面
     */
    public boolean isRedirectToErrorPage()
    {
        return redirectToErrorPage;
    }

    /**
     * 获取当前环境
     */
    public String getProfile()
    {
        return profile;
    }

    /**
     * 是否为生产环境
     */
    public boolean isProduction()
    {
        return "prod".equals(profile);
    }
}
