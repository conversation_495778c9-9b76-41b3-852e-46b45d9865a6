package com.ruoyi.ads.config;

import com.ruoyi.ads.interceptor.ClickRequestInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 广告模块Web配置
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Configuration
public class AdsWebConfig implements WebMvcConfigurer
{
    @Autowired
    private ClickRequestInterceptor clickRequestInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        // 注册点击请求拦截器（处理预检请求和防重复）
        registry.addInterceptor(clickRequestInterceptor)
                .addPathPatterns("/api/click")
                .order(1); // 设置较高优先级
    }
}
