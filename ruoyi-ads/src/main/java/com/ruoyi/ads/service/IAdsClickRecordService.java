package com.ruoyi.ads.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Date;
import java.util.Map;
import com.ruoyi.ads.domain.AdsClickRecord;

/**
 * 点击记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IAdsClickRecordService 
{
    /**
     * 查询点击记录
     * 
     * @param recordId 点击记录主键
     * @return 点击记录
     */
    public AdsClickRecord selectAdsClickRecordByRecordId(Long recordId);

    /**
     * 查询点击记录列表
     * 
     * @param adsClickRecord 点击记录
     * @return 点击记录集合
     */
    public List<AdsClickRecord> selectAdsClickRecordList(AdsClickRecord adsClickRecord);

    /**
     * 新增点击记录
     * 
     * @param adsClickRecord 点击记录
     * @return 结果
     */
    public int insertAdsClickRecord(AdsClickRecord adsClickRecord);

    /**
     * 修改点击记录
     * 
     * @param adsClickRecord 点击记录
     * @return 结果
     */
    public int updateAdsClickRecord(AdsClickRecord adsClickRecord);

    /**
     * 批量删除点击记录
     * 
     * @param recordIds 需要删除的点击记录主键集合
     * @return 结果
     */
    public int deleteAdsClickRecordByRecordIds(Long[] recordIds);

    /**
     * 删除点击记录信息
     * 
     * @param recordId 点击记录主键
     * @return 结果
     */
    public int deleteAdsClickRecordByRecordId(Long recordId);

    /**
     * 根据点击ID查询点击记录
     * 
     * @param clickId 点击ID
     * @return 点击记录
     */
    public AdsClickRecord selectAdsClickRecordByClickId(String clickId);

    /**
     * 根据配置ID查询点击记录列表
     * 
     * @param offerId 配置ID
     * @return 点击记录集合
     */
    public List<AdsClickRecord> selectAdsClickRecordByOfferId(Long offerId);

    /**
     * 根据开发者ID查询点击记录列表
     * 
     * @param publisherId 开发者ID
     * @return 点击记录集合
     */
    public List<AdsClickRecord> selectAdsClickRecordByPublisherId(Long publisherId);

    /**
     * 根据广告主ID查询点击记录列表
     * 
     * @param advertiserId 广告主ID
     * @return 点击记录集合
     */
    public List<AdsClickRecord> selectAdsClickRecordByAdvertiserId(Long advertiserId);

    /**
     * 根据广告ID查询点击记录列表
     * 
     * @param adsId 广告ID
     * @return 点击记录集合
     */
    public List<AdsClickRecord> selectAdsClickRecordByAdsId(Long adsId);

    /**
     * 根据时间范围查询点击记录列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 点击记录集合
     */
    public List<AdsClickRecord> selectAdsClickRecordByTimeRange(Date startTime, Date endTime);

    /**
     * 查询点击记录列表（包含关联信息）
     * 
     * @param adsClickRecord 点击记录
     * @return 点击记录集合
     */
    public List<AdsClickRecord> selectAdsClickRecordListWithRelated(AdsClickRecord adsClickRecord);

    /**
     * 统计点击记录总数
     * 
     * @return 总数
     */
    public Long countAdsClickRecord();

    /**
     * 根据条件统计点击记录数
     * 
     * @param adsClickRecord 点击记录
     * @return 统计数
     */
    public Long countAdsClickRecordByCondition(AdsClickRecord adsClickRecord);

    /**
     * 根据时间范围统计点击记录数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数
     */
    public Long countAdsClickRecordByTimeRange(Date startTime, Date endTime);

    /**
     * 异步记录点击数据
     * 
     * @param adsClickRecord 点击记录
     */
    public void recordClickAsync(AdsClickRecord adsClickRecord);

    /**
     * 清理过期的点击记录
     *
     * @param days 保留天数
     * @return 清理数量
     */
    public int cleanExpiredRecords(int days);

    // ========== 统计相关方法 ==========

    /**
     * 根据时间范围统计点击数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @return 点击数
     */
    public Long countByDateRange(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId);

    /**
     * 根据时间范围获取平均单价
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @return 平均单价
     */
    public Double getAvgPayoutByDateRange(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId);

    /**
     * 获取按广告主分布的点击统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param publisherId 开发者ID（可选）
     * @return 分布统计数据
     */
    public List<Map<String, Object>> getClickDistributionByAdvertiser(LocalDateTime startTime, LocalDateTime endTime, Integer publisherId);

    /**
     * 获取按国家分布的点击统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @return 分布统计数据
     */
    public List<Map<String, Object>> getClickDistributionByCountry(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId);

    /**
     * 获取点击统计表格数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @return 表格数据
     */
    public List<Map<String, Object>> getClickStatisticsTableData(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId);
}
