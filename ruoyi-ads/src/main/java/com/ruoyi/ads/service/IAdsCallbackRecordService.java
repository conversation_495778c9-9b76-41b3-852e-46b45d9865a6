package com.ruoyi.ads.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Date;
import java.util.Map;
import com.ruoyi.ads.domain.AdsCallbackRecord;

/**
 * 回调记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IAdsCallbackRecordService 
{
    /**
     * 查询回调记录
     * 
     * @param recordId 回调记录主键
     * @return 回调记录
     */
    public AdsCallbackRecord selectAdsCallbackRecordByRecordId(Long recordId);

    /**
     * 查询回调记录列表
     * 
     * @param adsCallbackRecord 回调记录
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordList(AdsCallbackRecord adsCallbackRecord);

    /**
     * 新增回调记录
     * 
     * @param adsCallbackRecord 回调记录
     * @return 结果
     */
    public int insertAdsCallbackRecord(AdsCallbackRecord adsCallbackRecord);

    /**
     * 修改回调记录
     * 
     * @param adsCallbackRecord 回调记录
     * @return 结果
     */
    public int updateAdsCallbackRecord(AdsCallbackRecord adsCallbackRecord);

    /**
     * 批量删除回调记录
     * 
     * @param recordIds 需要删除的回调记录主键集合
     * @return 结果
     */
    public int deleteAdsCallbackRecordByRecordIds(Long[] recordIds);

    /**
     * 删除回调记录信息
     * 
     * @param recordId 回调记录主键
     * @return 结果
     */
    public int deleteAdsCallbackRecordByRecordId(Long recordId);

    /**
     * 根据点击ID查询回调记录列表
     * 
     * @param clickId 点击ID
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordByClickId(String clickId);

    /**
     * 根据回调类型查询回调记录列表
     * 
     * @param callbackType 回调类型
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordByCallbackType(String callbackType);

    /**
     * 根据回调状态查询回调记录列表
     * 
     * @param callbackStatus 回调状态
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordByCallbackStatus(Integer callbackStatus);

    /**
     * 根据开发者ID查询回调记录列表
     * 
     * @param publisherId 开发者ID
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordByPublisherId(Long publisherId);

    /**
     * 根据广告主ID查询回调记录列表
     * 
     * @param advertiserId 广告主ID
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordByAdvertiserId(Long advertiserId);

    /**
     * 根据广告ID查询回调记录列表
     * 
     * @param adsId 广告ID
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordByAdsId(Long adsId);

    /**
     * 根据时间范围查询回调记录列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordByTimeRange(Date startTime, Date endTime);

    /**
     * 查询回调记录列表（包含关联信息）
     * 
     * @param adsCallbackRecord 回调记录
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectAdsCallbackRecordListWithRelated(AdsCallbackRecord adsCallbackRecord);

    /**
     * 查询待重试的回调记录列表
     * 
     * @return 回调记录集合
     */
    public List<AdsCallbackRecord> selectRetryableCallbackRecords();

    /**
     * 统计回调记录总数
     * 
     * @return 总数
     */
    public Long countAdsCallbackRecord();

    /**
     * 根据条件统计回调记录数
     * 
     * @param adsCallbackRecord 回调记录
     * @return 统计数
     */
    public Long countAdsCallbackRecordByCondition(AdsCallbackRecord adsCallbackRecord);

    /**
     * 根据回调状态统计回调记录数
     * 
     * @param callbackStatus 回调状态
     * @return 统计数
     */
    public Long countAdsCallbackRecordByCallbackStatus(Integer callbackStatus);

    /**
     * 根据时间范围统计回调记录数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数
     */
    public Long countAdsCallbackRecordByTimeRange(Date startTime, Date endTime);

    /**
     * 异步处理回调
     * 
     * @param adsCallbackRecord 回调记录
     */
    public void processCallbackAsync(AdsCallbackRecord adsCallbackRecord);

    /**
     * 重试失败的回调
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    public int retryCallback(Long recordId);

    /**
     * 清理过期的回调记录
     *
     * @param days 保留天数
     * @return 清理数量
     */
    public int cleanExpiredRecords(int days);

    // ========== 统计相关方法 ==========

    /**
     * 根据时间范围统计回调数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @param callbackType 回调类型（可选）
     * @return 回调数
     */
    public Long countByDateRange(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId, String callbackType);

    /**
     * 根据状态和时间范围统计回调数
     *
     * @param status 回调状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @param callbackType 回调类型（可选）
     * @return 回调数
     */
    public Long countByStatusAndDateRange(Integer status, LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId, String callbackType);

    /**
     * 获取按回调类型分布的统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @return 分布统计数据
     */
    public List<Map<String, Object>> getCallbackDistributionByType(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId);

    /**
     * 获取按状态分布的统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @param callbackType 回调类型（可选）
     * @return 分布统计数据
     */
    public List<Map<String, Object>> getCallbackDistributionByStatus(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId, String callbackType);

    /**
     * 获取回调统计表格数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param advertiserId 广告主ID（可选）
     * @param publisherId 开发者ID（可选）
     * @param callbackType 回调类型（可选）
     * @return 表格数据
     */
    public List<Map<String, Object>> getCallbackStatisticsTableData(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId, String callbackType);
}
