package com.ruoyi.ads.service.impl;

import com.ruoyi.ads.domain.AdsInfo;
import com.ruoyi.ads.domain.AdsPartner;
import com.ruoyi.ads.domain.AdsPublisherOffer;
import com.ruoyi.ads.mapper.AdsInfoMapper;
import com.ruoyi.ads.mapper.AdsPartnerMapper;
import com.ruoyi.ads.mapper.AdsPublisherOfferMapper;
import com.ruoyi.ads.service.IAdsPublisherOfferService;
import com.ruoyi.ads.service.IAdsCacheService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ruoyi.common.utils.ShiroUtils.getLoginName;

/**
 * 广告配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class AdsPublisherOfferServiceImpl implements IAdsPublisherOfferService 
{
    @Autowired
    private AdsPublisherOfferMapper adsPublisherOfferMapper;
    
    @Autowired
    private AdsPartnerMapper adsPartnerMapper;
    
    @Autowired
    private AdsInfoMapper adsInfoMapper;

    @Autowired
    private IAdsCacheService adsCacheService;

    @Value("${ruoyi.domain}")
    private String domain;

    /**
     * 查询广告配置
     *
     * @param offerId 广告配置主键
     * @return 广告配置
     */
    @Override
    public AdsPublisherOffer selectAdsPublisherOfferByOfferId(Long offerId)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferByOfferId(offerId);
    }

    /**
     * 查询广告配置（带关联信息）
     *
     * @param offerId 广告配置主键
     * @return 广告配置（包含广告名称、广告主名称、开发者名称）
     */
    @Override
    public AdsPublisherOffer selectAdsPublisherOfferWithRelatedByOfferId(Long offerId)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferWithRelatedByOfferId(offerId);
    }

    /**
     * 查询广告配置列表
     * 
     * @param adsPublisherOffer 广告配置
     * @return 广告配置
     */
    @Override
    public List<AdsPublisherOffer> selectAdsPublisherOfferList(AdsPublisherOffer adsPublisherOffer)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferList(adsPublisherOffer);
    }

    /**
     * 新增广告配置
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdsPublisherOffer(AdsPublisherOffer adsPublisherOffer)
    {
        // 校验广告和开发者组合是否唯一
        if (!checkAdsPublisherUnique(adsPublisherOffer).equals("0"))
        {
            throw new ServiceException("新增广告配置失败，该广告已配置给此开发者");
        }
        
        // 验证广告主、开发者、广告是否存在
        validateRelatedEntities(adsPublisherOffer);
        
        // 生成点击链接（使用占位符）
        if (StringUtils.isEmpty(adsPublisherOffer.getClickUrl()))
        {
            AdsPartner publisher = adsPartnerMapper.selectAdsPartnerByPartnerId(adsPublisherOffer.getPublisherId());
            if (publisher != null)
            {
                String clickUrl = generateClickUrl(publisher.getPartnerKey(), null);
                adsPublisherOffer.setClickUrl(clickUrl);
            }
        }

        adsPublisherOffer.setCreateBy(getLoginName());
        adsPublisherOffer.setCreateTime(DateUtils.getNowDate());

        // 先保存记录
        int result = adsPublisherOfferMapper.insertAdsPublisherOffer(adsPublisherOffer);

        // 保存成功后，使用真实的offerId更新点击链接
        if (result > 0 && adsPublisherOffer.getOfferId() != null &&
            StringUtils.isNotEmpty(adsPublisherOffer.getClickUrl()) &&
            adsPublisherOffer.getClickUrl().contains("{offer_id}"))
        {
            AdsPartner publisher = adsPartnerMapper.selectAdsPartnerByPartnerId(adsPublisherOffer.getPublisherId());
            if (publisher != null)
            {
                String realClickUrl = generateClickUrl(publisher.getPartnerKey(), adsPublisherOffer.getOfferId());
                adsPublisherOffer.setClickUrl(realClickUrl);
                adsPublisherOfferMapper.updateAdsPublisherOffer(adsPublisherOffer);
            }
        }

        return result;
    }

    /**
     * 修改广告配置
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdsPublisherOffer(AdsPublisherOffer adsPublisherOffer)
    {
        // 校验广告和开发者组合是否唯一
        if (!checkAdsPublisherUnique(adsPublisherOffer).equals("0"))
        {
            throw new ServiceException("修改广告配置失败，该广告已配置给此开发者");
        }
        
        // 验证广告主、开发者、广告是否存在
        validateRelatedEntities(adsPublisherOffer);
        
        adsPublisherOffer.setUpdateBy(getLoginName());
        adsPublisherOffer.setUpdateTime(DateUtils.getNowDate());
        int result = adsPublisherOfferMapper.updateAdsPublisherOffer(adsPublisherOffer);

        // 清除缓存
        if (result > 0) {
            adsCacheService.evictPublisherOfferCache(adsPublisherOffer.getOfferId());
        }

        return result;
    }

    /**
     * 批量删除广告配置
     *
     * @param offerIds 需要删除的广告配置主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdsPublisherOfferByOfferIds(Long[] offerIds)
    {
        int result = adsPublisherOfferMapper.deleteAdsPublisherOfferByOfferIds(offerIds);

        // 清除缓存
        if (result > 0) {
            for (Long offerId : offerIds) {
                adsCacheService.evictPublisherOfferCache(offerId);
            }
        }

        return result;
    }

    /**
     * 批量删除广告配置
     *
     * @param ids 需要删除的广告配置主键集合字符串
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdsPublisherOfferByOfferIds(String ids)
    {
        Long[] offerIds = Convert.toLongArray(ids);
        int result = adsPublisherOfferMapper.deleteAdsPublisherOfferByOfferIds(offerIds);

        // 清除缓存
        if (result > 0) {
            for (Long offerId : offerIds) {
                adsCacheService.evictPublisherOfferCache(offerId);
            }
        }

        return result;
    }

    /**
     * 删除广告配置信息
     * 
     * @param offerId 广告配置主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdsPublisherOfferByOfferId(Long offerId)
    {
        int result = adsPublisherOfferMapper.deleteAdsPublisherOfferByOfferId(offerId);

        // 清除缓存
        if (result > 0) {
            adsCacheService.evictPublisherOfferCache(offerId);
        }

        return result;
    }

    /**
     * 根据广告ID和开发者ID查询配置
     * 
     * @param adsId 广告ID
     * @param publisherId 开发者ID
     * @return 广告配置
     */
    @Override
    public AdsPublisherOffer selectAdsPublisherOfferByAdsIdAndPublisherId(Long adsId, Long publisherId)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferByAdsIdAndPublisherId(adsId, publisherId);
    }

    /**
     * 根据广告主ID查询配置列表
     * 
     * @param advertiserId 广告主ID
     * @return 广告配置集合
     */
    @Override
    public List<AdsPublisherOffer> selectAdsPublisherOfferByAdvertiserId(Long advertiserId)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferByAdvertiserId(advertiserId);
    }

    /**
     * 根据开发者ID查询配置列表
     * 
     * @param publisherId 开发者ID
     * @return 广告配置集合
     */
    @Override
    public List<AdsPublisherOffer> selectAdsPublisherOfferByPublisherId(Long publisherId)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferByPublisherId(publisherId);
    }

    /**
     * 根据广告ID查询配置列表
     * 
     * @param adsId 广告ID
     * @return 广告配置集合
     */
    @Override
    public List<AdsPublisherOffer> selectAdsPublisherOfferByAdsId(Long adsId)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferByAdsId(adsId);
    }

    /**
     * 根据状态查询配置列表
     * 
     * @param status 状态
     * @return 广告配置集合
     */
    @Override
    public List<AdsPublisherOffer> selectAdsPublisherOfferByStatus(String status)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferByStatus(status);
    }

    /**
     * 校验广告和开发者组合是否唯一
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    @Override
    public String checkAdsPublisherUnique(AdsPublisherOffer adsPublisherOffer)
    {
        Long offerId = StringUtils.isNull(adsPublisherOffer.getOfferId()) ? -1L : adsPublisherOffer.getOfferId();
        AdsPublisherOffer offer = adsPublisherOfferMapper.checkAdsPublisherUnique(adsPublisherOffer);
        if (StringUtils.isNotNull(offer) && offer.getOfferId().longValue() != offerId.longValue())
        {
            return "1";
        }
        return "0";
    }

    /**
     * 查询广告配置列表（包含关联信息）
     * 
     * @param adsPublisherOffer 广告配置
     * @return 广告配置集合
     */
    @Override
    public List<AdsPublisherOffer> selectAdsPublisherOfferListWithRelated(AdsPublisherOffer adsPublisherOffer)
    {
        return adsPublisherOfferMapper.selectAdsPublisherOfferListWithRelated(adsPublisherOffer);
    }

    /**
     * 生成点击链接
     *
     * @param partnerKey 开发者密钥
     * @param offerId 配置ID，如果为null则使用占位符
     * @return 点击链接
     */
    @Override
    public String generateClickUrl(String partnerKey, Long offerId)
    {
        // 如果offerId为null，使用占位符
        if (offerId == null) {
            return String.format(domain + "/api/click?offer_id={offer_id}&key=%s", partnerKey);
        }
        // 使用真实的offerId生成点击链接
        return String.format(domain + "/api/click?offer_id=%d&key=%s", offerId, partnerKey);
    }

    /**
     * 批量创建广告配置
     * 
     * @param advertiserId 广告主ID
     * @param publisherId 开发者ID
     * @param adsIds 广告ID列表
     * @param adsPublisherOffer 配置模板
     * @return 结果
     */
    @Override
    @Transactional
    public int batchCreateOffers(Long advertiserId, Long publisherId, Long[] adsIds, AdsPublisherOffer adsPublisherOffer)
    {
        if (adsIds == null || adsIds.length == 0)
        {
            throw new ServiceException("广告ID列表不能为空");
        }
        
        // 获取开发者信息
        AdsPartner publisher = adsPartnerMapper.selectAdsPartnerByPartnerId(publisherId);
        if (publisher == null)
        {
            throw new ServiceException("开发者不存在");
        }
        
        int successCount = 0;
        for (Long adsId : adsIds)
        {
            // 检查是否已存在配置
            AdsPublisherOffer existingOffer = adsPublisherOfferMapper.selectAdsPublisherOfferByAdsIdAndPublisherId(adsId, publisherId);
            if (existingOffer != null)
            {
                continue; // 跳过已存在的配置
            }
            
            // 创建新配置
            AdsPublisherOffer newOffer = new AdsPublisherOffer();
            newOffer.setAdvertiserId(advertiserId);
            newOffer.setPublisherId(publisherId);
            newOffer.setAdsId(adsId);
            newOffer.setPayout(adsPublisherOffer.getPayout());
            newOffer.setCountry(adsPublisherOffer.getCountry());
            newOffer.setInstallCap(adsPublisherOffer.getInstallCap());
            newOffer.setDailyCap(adsPublisherOffer.getDailyCap());
            newOffer.setPriority(adsPublisherOffer.getPriority());
            newOffer.setStatus(adsPublisherOffer.getStatus());
            newOffer.setRemark(adsPublisherOffer.getRemark());
            
            // 生成点击链接（使用占位符）
            String clickUrl = generateClickUrl(publisher.getPartnerKey(), null);
            newOffer.setClickUrl(clickUrl);

            newOffer.setCreateBy(getLoginName());
            newOffer.setCreateTime(DateUtils.getNowDate());

            // 先保存记录
            if (adsPublisherOfferMapper.insertAdsPublisherOffer(newOffer) > 0)
            {
                // 保存成功后，使用真实的offerId更新点击链接
                if (newOffer.getOfferId() != null)
                {
                    String realClickUrl = generateClickUrl(publisher.getPartnerKey(), newOffer.getOfferId());
                    newOffer.setClickUrl(realClickUrl);
                    adsPublisherOfferMapper.updateAdsPublisherOffer(newOffer);
                }
                successCount++;
            }
        }
        
        return successCount;
    }

    /**
     * 验证关联实体是否存在
     * 
     * @param adsPublisherOffer 广告配置
     */
    private void validateRelatedEntities(AdsPublisherOffer adsPublisherOffer)
    {
        // 验证广告主
        AdsPartner advertiser = adsPartnerMapper.selectAdsPartnerByPartnerId(adsPublisherOffer.getAdvertiserId());
        if (advertiser == null || !advertiser.isAdvertiser())
        {
            throw new ServiceException("广告主不存在或类型错误");
        }
        
        // 验证开发者
        AdsPartner publisher = adsPartnerMapper.selectAdsPartnerByPartnerId(adsPublisherOffer.getPublisherId());
        if (publisher == null || !publisher.isPublisher())
        {
            throw new ServiceException("开发者不存在或类型错误");
        }
        
        // 验证广告
        AdsInfo adsInfo = adsInfoMapper.selectAdsInfoByAdsId(adsPublisherOffer.getAdsId());
        if (adsInfo == null)
        {
            throw new ServiceException("广告不存在");
        }
        
        // 验证广告主和广告的关联关系
        if (!adsInfo.getAdvertiserId().equals(adsPublisherOffer.getAdvertiserId()))
        {
            throw new ServiceException("广告不属于指定的广告主");
        }
    }
}
