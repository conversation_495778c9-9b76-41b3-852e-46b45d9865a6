package com.ruoyi.ads.service.impl;

import com.ruoyi.ads.domain.*;
import com.ruoyi.ads.service.*;
import com.ruoyi.ads.util.ClickIdGenerator;
import com.ruoyi.ads.util.UrlMacroReplacer;
import com.ruoyi.common.utils.IpUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 广告点击追踪服务实现
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class AdsClickTrackingServiceImpl implements IAdsClickTrackingService
{
    private static final Logger logger = LoggerFactory.getLogger(AdsClickTrackingServiceImpl.class);
    
    // 防重复请求缓存，存储请求键和最后请求时间
    private final Map<String, Long> requestCache = new ConcurrentHashMap<>();
    
    // 缓存清理间隔（毫秒）
    private static final long CACHE_CLEANUP_INTERVAL = 60000; // 1分钟
    
    // 重复请求检测时间窗口（毫秒）
    private static final long DUPLICATE_REQUEST_WINDOW = 2000; // 2秒
    
    // 上次缓存清理时间
    private volatile long lastCleanupTime = System.currentTimeMillis();

    @Autowired
    private IAdsClickRecordService adsClickRecordService;
    
    @Autowired
    private IAdsCallbackRecordService adsCallbackRecordService;
    
    @Autowired
    private IAdsPublisherOfferService adsPublisherOfferService;
    
    @Autowired
    private IAdsPartnerService adsPartnerService;

    @Autowired
    private IAdsInfoService adsInfoService;

    @Autowired
    private IAdsCacheService adsCacheService;

    @Override
    public String processClickRequest(Long offerId, String partnerKey, String deviceId,
                                    String subParam1, String subParam2, String subParam3,
                                    String channel, HttpServletRequest request)
    {
        // 获取请求信息
        String ipAddress = IpUtils.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        // 生成请求键用于防重复检测
        String requestKey = generateRequestKey(offerId, partnerKey, deviceId, ipAddress);

        // 使用同步块确保防重复检查和缓存更新的原子性
        synchronized (this) {
            // 检查重复请求
            if (!isValidClickRequest(offerId, partnerKey, ipAddress, deviceId))
            {
                logger.warn("检测到重复或无效的点击请求: offerId={}, partnerKey={}, ip={}",
                           offerId, partnerKey, ipAddress);
                throw new IllegalArgumentException("Duplicate or invalid click request");
            }

            // 立即记录请求时间用于防重复检测，防止并发请求
            requestCache.put(requestKey, System.currentTimeMillis());
        }

        try {
            // 验证合作伙伴（使用缓存）
            AdsPartner partner = adsCacheService.getPartnerByKey(partnerKey);
            if (partner == null)
            {
                throw new SecurityException("合作伙伴密钥无效: " + partnerKey);
            }
            if (!partner.isPublisher())
            {
                throw new SecurityException("合作伙伴不是开发者类型: " + partnerKey);
            }

            // 验证广告配置（使用缓存）
            AdsPublisherOffer offer = adsCacheService.getPublisherOfferById(offerId);
            if (offer == null)
            {
                throw new IllegalArgumentException("广告配置不存在: offer_id=" + offerId);
            }
            if (!offer.getPublisherId().equals(partner.getPartnerId()))
            {
                throw new IllegalArgumentException("广告配置与开发者不匹配: offer_id=" + offerId + ", publisher_id=" + partner.getPartnerId());
            }

            // 获取广告信息
            AdsInfo adsInfo = adsInfoService.selectAdsInfoByAdsId(offer.getAdsId());
            if (adsInfo == null) {
                throw new IllegalArgumentException("广告信息不存在: ads_id=" + offer.getAdsId());
            }
            if (!"0".equals(adsInfo.getStatus())) {
                throw new IllegalArgumentException("广告已停用: ads_id=" + offer.getAdsId() + ", status=" + adsInfo.getStatus());
            }

            // 生成点击ID
            String clickId = ClickIdGenerator.generateClickId(offerId, deviceId, partnerKey,
                    subParam1, subParam2, subParam3, channel, ipAddress, userAgent,
                    partner.getPartnerId().toString());

            // 创建点击记录
            AdsClickRecord clickRecord = createClickRecord(clickId, offerId, partner, offer,
                    deviceId, ipAddress, userAgent, subParam1, subParam2, subParam3, channel);

            //
            // 构建宏参数
            Map<String, String> macros = UrlMacroReplacer.buildMacros(clickId, partnerKey, deviceId,
                    ipAddress, userAgent, subParam1, subParam2, subParam3, channel);

            // 替换目标URL中的宏参数
            String targetUrl = adsInfo.getClickUrl();
            if (StringUtils.isNotEmpty(targetUrl))
            {
                targetUrl = UrlMacroReplacer.replaceMacros(targetUrl, macros);
                clickRecord.setTargetUrl(targetUrl);
            }

            // 异步记录点击数据（带防重复机制）
            recordClickSafely(clickRecord);

            // 定期清理过期缓存
            cleanupExpiredCache();

            return targetUrl;
        } catch (Exception e) {
            // 如果处理失败，移除缓存记录，允许重试
            requestCache.remove(requestKey);
            throw e;
        }
    }

    @Override
    public boolean processCallback(String clickId, String callbackType, String eventName,
                                 String eventValue, HttpServletRequest request)
    {
        try
        {
            // 验证点击ID
            if (!ClickIdGenerator.isValidClickId(clickId))
            {
                throw new IllegalArgumentException("Invalid click ID");
            }

            // 查询点击记录
            AdsClickRecord clickRecord = adsClickRecordService.selectAdsClickRecordByClickId(clickId);
            if (clickRecord == null)
            {
                throw new IllegalArgumentException("Click record not found");
            }

            // 创建回调记录
            AdsCallbackRecord callbackRecord = new AdsCallbackRecord();
            callbackRecord.setClickId(clickId);
            callbackRecord.setCallbackType(callbackType);
            callbackRecord.setEventName(eventName);
            callbackRecord.setEventValue(eventValue);
            callbackRecord.setOfferId(clickRecord.getOfferId());
            callbackRecord.setPublisherId(clickRecord.getPublisherId());
            callbackRecord.setAdvertiserId(clickRecord.getAdvertiserId());
            callbackRecord.setAdsId(clickRecord.getAdsId());
            callbackRecord.setCallbackTime(new Date());
            callbackRecord.setCallbackStatus(1); // 成功
            callbackRecord.setRetryCount(0);
            callbackRecord.setStatus("0");

            // 异步处理回调
            adsCallbackRecordService.processCallbackAsync(callbackRecord);

            return true;
        }
        catch (Exception e)
        {
            logger.error("回调处理失败: clickId={}", clickId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getClickStats(String partnerKey, String startDate, String endDate)
    {
        Map<String, Object> stats = new HashMap<>();
        try
        {
            Long totalClicks = adsClickRecordService.countAdsClickRecord();
            stats.put("total_clicks", totalClicks);
            stats.put("partner_key", partnerKey);
            stats.put("start_date", startDate);
            stats.put("end_date", endDate);
        }
        catch (Exception e)
        {
            logger.error("获取点击统计失败", e);
            stats.put("error", "Failed to get statistics");
        }
        return stats;
    }

    @Override
    public Map<String, Object> getCallbackStats(String partnerKey, String startDate, String endDate)
    {
        Map<String, Object> stats = new HashMap<>();
        try
        {
            Long totalCallbacks = adsCallbackRecordService.countAdsCallbackRecord();
            stats.put("total_callbacks", totalCallbacks);
            stats.put("partner_key", partnerKey);
            stats.put("start_date", startDate);
            stats.put("end_date", endDate);
        }
        catch (Exception e)
        {
            logger.error("获取回调统计失败", e);
            stats.put("error", "Failed to get statistics");
        }
        return stats;
    }

    @Override
    public boolean isValidClickRequest(Long offerId, String partnerKey, String ipAddress, String deviceId)
    {
        String requestKey = generateRequestKey(offerId, partnerKey, deviceId, ipAddress);
        Long lastRequestTime = requestCache.get(requestKey);
        long currentTime = System.currentTimeMillis();
        
        // 检查是否在重复请求时间窗口内
        if (lastRequestTime != null && (currentTime - lastRequestTime) < DUPLICATE_REQUEST_WINDOW)
        {
            return false;
        }
        
        return true;
    }

    @Override
    public String generateRequestKey(Long offerId, String partnerKey, String deviceId, String ipAddress)
    {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("click_")
                  .append(offerId != null ? offerId : "")
                  .append("_")
                  .append(partnerKey != null ? partnerKey : "")
                  .append("_")
                  .append(deviceId != null ? deviceId : "")
                  .append("_")
                  .append(ipAddress != null ? ipAddress : "");
        return keyBuilder.toString();
    }

    /**
     * 创建点击记录
     */
    private AdsClickRecord createClickRecord(String clickId, Long offerId, AdsPartner partner,
                                           AdsPublisherOffer offer, String deviceId, String ipAddress,
                                           String userAgent, String subParam1, String subParam2,
                                           String subParam3, String channel)
    {
        AdsClickRecord clickRecord = new AdsClickRecord();
        clickRecord.setClickId(clickId);
        clickRecord.setOfferId(offerId);
        clickRecord.setPublisherId(partner.getPartnerId());
        clickRecord.setAdvertiserId(offer.getAdvertiserId());
        clickRecord.setAdsId(offer.getAdsId());
        clickRecord.setDeviceId(deviceId);
        clickRecord.setIpAddress(ipAddress);
        clickRecord.setUserAgent(userAgent);
        clickRecord.setSubParam1(subParam1);
        clickRecord.setSubParam2(subParam2);
        clickRecord.setSubParam3(subParam3);
        clickRecord.setChannel(channel);
        clickRecord.setPayout(offer.getPayout());
        clickRecord.setClickTime(new Date());
        clickRecord.setStatus("0");
        return clickRecord;
    }

    /**
     * 安全地记录点击数据（防重复）
     */
    private void recordClickSafely(AdsClickRecord clickRecord)
    {
        try
        {
            // 使用点击ID作为锁，防止同一个点击ID的并发处理
            String lockKey = "click_lock_" + clickRecord.getClickId();
            synchronized (lockKey.intern()) {
                // 双重检查是否已存在相同的点击记录
                AdsClickRecord existingRecord = adsClickRecordService.selectAdsClickRecordByClickId(clickRecord.getClickId());
                if (existingRecord != null)
                {
                    logger.debug("点击记录已存在，跳过保存: clickId={}", clickRecord.getClickId());
                    return;
                }

                // 异步记录点击数据
                adsClickRecordService.recordClickAsync(clickRecord);

                logger.info("点击记录创建成功: clickId={}, offerId={}, publisherId={}",
                           clickRecord.getClickId(), clickRecord.getOfferId(), clickRecord.getPublisherId());
            }
        }
        catch (Exception e)
        {
            logger.error("记录点击数据失败: clickId={}", clickRecord.getClickId(), e);
        }
    }

    /**
     * 清理过期的缓存项
     */
    private void cleanupExpiredCache()
    {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCleanupTime > CACHE_CLEANUP_INTERVAL)
        {
            synchronized (this)
            {
                if (currentTime - lastCleanupTime > CACHE_CLEANUP_INTERVAL)
                {
                    Iterator<Map.Entry<String, Long>> iterator = requestCache.entrySet().iterator();
                    while (iterator.hasNext())
                    {
                        Map.Entry<String, Long> entry = iterator.next();
                        if (currentTime - entry.getValue() > DUPLICATE_REQUEST_WINDOW * 5) // 保留5倍时间窗口的数据
                        {
                            iterator.remove();
                        }
                    }
                    lastCleanupTime = currentTime;
                    logger.debug("清理过期缓存完成，当前缓存大小: {}", requestCache.size());
                }
            }
        }
    }
}
