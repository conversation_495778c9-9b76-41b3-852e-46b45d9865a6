package com.ruoyi.ads.service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 广告点击追踪服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IAdsClickTrackingService 
{
    /**
     * 处理点击请求
     * 
     * @param offerId 配置ID
     * @param partnerKey 合作伙伴密钥
     * @param deviceId 设备ID
     * @param subParam1 附属参数1
     * @param subParam2 附属参数2
     * @param subParam3 附属参数3
     * @param channel 渠道
     * @param request HTTP请求
     * @return 目标URL
     * @throws IllegalArgumentException 参数错误
     * @throws SecurityException 权限错误
     */
    String processClickRequest(Long offerId, String partnerKey, String deviceId,
                              String subParam1, String subParam2, String subParam3,
                              String channel, HttpServletRequest request);

    /**
     * 处理回调请求
     * 
     * @param clickId 点击ID
     * @param callbackType 回调类型
     * @param eventName 事件名称
     * @param eventValue 事件值
     * @param request HTTP请求
     * @return 处理结果
     * @throws IllegalArgumentException 参数错误
     */
    boolean processCallback(String clickId, String callbackType, String eventName,
                           String eventValue, HttpServletRequest request);

    /**
     * 获取点击统计
     * 
     * @param partnerKey 合作伙伴密钥
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    Map<String, Object> getClickStats(String partnerKey, String startDate, String endDate);

    /**
     * 获取回调统计
     * 
     * @param partnerKey 合作伙伴密钥
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    Map<String, Object> getCallbackStats(String partnerKey, String startDate, String endDate);

    /**
     * 验证点击请求的有效性
     * 
     * @param offerId 配置ID
     * @param partnerKey 合作伙伴密钥
     * @param ipAddress IP地址
     * @param deviceId 设备ID
     * @return 是否有效
     */
    boolean isValidClickRequest(Long offerId, String partnerKey, String ipAddress, String deviceId);

    /**
     * 生成防重复请求的键
     * 
     * @param offerId 配置ID
     * @param partnerKey 合作伙伴密钥
     * @param deviceId 设备ID
     * @param ipAddress IP地址
     * @return 请求键
     */
    String generateRequestKey(Long offerId, String partnerKey, String deviceId, String ipAddress);
}
