package com.ruoyi.ads.service.impl;

import com.ruoyi.ads.service.IAdsCallbackHttpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

/**
 * 回调HTTP服务实现
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Service
public class AdsCallbackHttpServiceImpl implements IAdsCallbackHttpService
{
    private static final Logger logger = LoggerFactory.getLogger(AdsCallbackHttpServiceImpl.class);
    
    // 默认超时时间（毫秒）
    private static final int DEFAULT_TIMEOUT = 10000;
    
    // HTTP客户端
    private final HttpClient httpClient;
    
    public AdsCallbackHttpServiceImpl() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofMillis(DEFAULT_TIMEOUT))
                .build();
    }
    
    @Override
    public CallbackResult sendCallback(String url) {
        return sendCallback(url, DEFAULT_TIMEOUT);
    }
    
    @Override
    public CallbackResult sendCallback(String url, int timeout) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("发送回调请求: url={}, timeout={}ms", url, timeout);
            
            // 构建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofMillis(timeout))
                    .header("User-Agent", "MobinovaxAds/1.0")
                    .header("Content-Type", "application/x-www-form-urlencoded")
                    .GET()
                    .build();
            
            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            long responseTime = System.currentTimeMillis() - startTime;
            int statusCode = response.statusCode();
            String responseBody = response.body();
            
            logger.info("回调响应: url={}, statusCode={}, responseTime={}ms, bodyLength={}", 
                       url, statusCode, responseTime, responseBody != null ? responseBody.length() : 0);
            
            // 判断是否成功（2xx状态码认为成功）
            if (statusCode >= 200 && statusCode < 300) {
                return CallbackResult.success(statusCode, responseBody, responseTime);
            } else {
                logger.warn("回调失败: url={}, statusCode={}, responseBody={}", url, statusCode, responseBody);
                return CallbackResult.failure(statusCode, responseBody, responseTime);
            }
            
        } catch (IOException e) {
            long responseTime = System.currentTimeMillis() - startTime;
            String errorMessage = "网络异常: " + e.getMessage();
            logger.error("回调网络异常: url={}, error={}", url, errorMessage, e);
            return CallbackResult.failure(errorMessage, responseTime);
            
        } catch (InterruptedException e) {
            long responseTime = System.currentTimeMillis() - startTime;
            String errorMessage = "请求被中断: " + e.getMessage();
            logger.error("回调请求被中断: url={}, error={}", url, errorMessage, e);
            Thread.currentThread().interrupt(); // 恢复中断状态
            return CallbackResult.failure(errorMessage, responseTime);
            
        } catch (IllegalArgumentException e) {
            long responseTime = System.currentTimeMillis() - startTime;
            String errorMessage = "URL格式错误: " + e.getMessage();
            logger.error("回调URL格式错误: url={}, error={}", url, errorMessage, e);
            return CallbackResult.failure(errorMessage, responseTime);
            
        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            String errorMessage = "未知异常: " + e.getMessage();
            logger.error("回调未知异常: url={}, error={}", url, errorMessage, e);
            return CallbackResult.failure(errorMessage, responseTime);
        }
    }
}
