package com.ruoyi.ads.service.impl;

import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.ruoyi.ads.domain.*;
import com.ruoyi.ads.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Service;

/**
 * 广告缓存服务实现
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Service
public class AdsCacheServiceImpl implements IAdsCacheService
{
    private static final Logger logger = LoggerFactory.getLogger(AdsCacheServiceImpl.class);
    
    @Autowired
    @Qualifier("caffeineCacheManager")
    private CacheManager cacheManager;
    
    @Autowired
    private IAdsPartnerService adsPartnerService;
    
    @Autowired
    private IAdsInfoService adsInfoService;
    
    @Autowired
    private IAdsPublisherOfferService adsPublisherOfferService;
    
    @Override
    @Cacheable(value = "adsPartnerCache", key = "#partnerId", cacheManager = "caffeineCacheManager")
    public AdsPartner getPartnerById(Long partnerId)
    {
        logger.debug("从数据库查询合作伙伴信息: partnerId={}", partnerId);
        return adsPartnerService.selectAdsPartnerByPartnerId(partnerId);
    }
    
    @Override
    @Cacheable(value = "adsPartnerCache", key = "'key_' + #partnerKey", cacheManager = "caffeineCacheManager")
    public AdsPartner getPartnerByKey(String partnerKey)
    {
        logger.debug("从数据库查询合作伙伴信息: partnerKey={}", partnerKey);
        return adsPartnerService.selectAdsPartnerByPartnerKey(partnerKey);
    }
    
    @Override
    @Cacheable(value = "adsInfoCache", key = "#adsId", cacheManager = "caffeineCacheManager")
    public AdsInfo getAdsInfoById(Long adsId)
    {
        logger.debug("从数据库查询广告信息: adsId={}", adsId);
        return adsInfoService.selectAdsInfoByAdsId(adsId);
    }
    
    @Override
    @Cacheable(value = "adsPublisherOfferCache", key = "#offerId", cacheManager = "caffeineCacheManager")
    public AdsPublisherOffer getPublisherOfferById(Long offerId)
    {
        logger.debug("从数据库查询发布者配置: offerId={}", offerId);
        return adsPublisherOfferService.selectAdsPublisherOfferByOfferId(offerId);
    }
    
    // 注意：当前系统中没有独立的AdsAdvertiserOffer，使用AdsPublisherOffer代替
    @Override
    @Cacheable(value = "adsAdvertiserOfferCache", key = "#offerId", cacheManager = "caffeineCacheManager")
    public Object getAdvertiserOfferById(Long offerId)
    {
        logger.debug("从数据库查询广告主配置: offerId={}", offerId);
        // 暂时返回null，因为系统中没有独立的AdsAdvertiserOffer实体
        return null;
    }
    
    @Override
    @CacheEvict(value = "adsPartnerCache", key = "#partnerId", cacheManager = "caffeineCacheManager")
    public void evictPartnerCache(Long partnerId)
    {
        logger.info("清除合作伙伴缓存: partnerId={}", partnerId);
    }
    
    @Override
    @CacheEvict(value = "adsPartnerCache", key = "'key_' + #partnerKey", cacheManager = "caffeineCacheManager")
    public void evictPartnerKeyCache(String partnerKey)
    {
        logger.info("清除合作伙伴Key缓存: partnerKey={}", partnerKey);
    }
    
    @Override
    @CacheEvict(value = "adsInfoCache", key = "#adsId", cacheManager = "caffeineCacheManager")
    public void evictAdsInfoCache(Long adsId)
    {
        logger.info("清除广告信息缓存: adsId={}", adsId);
    }
    
    @Override
    @CacheEvict(value = "adsPublisherOfferCache", key = "#offerId", cacheManager = "caffeineCacheManager")
    public void evictPublisherOfferCache(Long offerId)
    {
        logger.info("清除发布者配置缓存: offerId={}", offerId);
    }
    
    @Override
    @CacheEvict(value = "adsAdvertiserOfferCache", key = "#offerId", cacheManager = "caffeineCacheManager")
    public void evictAdvertiserOfferCache(Long offerId)
    {
        logger.info("清除广告主配置缓存: offerId={}", offerId);
    }
    
    @Override
    @CacheEvict(value = {"adsPartnerCache", "adsInfoCache", "adsPublisherOfferCache", "adsAdvertiserOfferCache"}, 
                allEntries = true, cacheManager = "caffeineCacheManager")
    public void evictAllCache()
    {
        logger.info("清除所有Caffeine缓存");
    }
    
    @Override
    public String getCacheStats()
    {
        StringBuilder stats = new StringBuilder();
        stats.append("Caffeine缓存统计信息:\n");
        
        String[] cacheNames = {"adsPartnerCache", "adsInfoCache", "adsPublisherOfferCache", "adsAdvertiserOfferCache"};
        
        for (String cacheName : cacheNames) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache instanceof CaffeineCache) {
                CaffeineCache caffeineCache = (CaffeineCache) cache;
                com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = caffeineCache.getNativeCache();
                CacheStats cacheStats = nativeCache.stats();
                
                stats.append(String.format("\n%s:\n", cacheName));
                stats.append(String.format("  缓存大小: %d\n", nativeCache.estimatedSize()));
                stats.append(String.format("  命中次数: %d\n", cacheStats.hitCount()));
                stats.append(String.format("  未命中次数: %d\n", cacheStats.missCount()));
                stats.append(String.format("  命中率: %.2f%%\n", cacheStats.hitRate() * 100));
                stats.append(String.format("  加载次数: %d\n", cacheStats.loadCount()));
                stats.append(String.format("  平均加载时间: %.2fms\n", cacheStats.averageLoadPenalty() / 1_000_000.0));
                stats.append(String.format("  驱逐次数: %d\n", cacheStats.evictionCount()));
            }
        }
        
        return stats.toString();
    }
}
