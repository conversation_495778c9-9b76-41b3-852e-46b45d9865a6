package com.ruoyi.ads.service;

import com.ruoyi.ads.domain.AdsPartner;
import com.ruoyi.ads.domain.AdsInfo;
import com.ruoyi.ads.domain.AdsPublisherOffer;

/**
 * 广告缓存服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface IAdsCacheService 
{
    /**
     * 获取合作伙伴信息（带缓存）
     * 
     * @param partnerId 合作伙伴ID
     * @return 合作伙伴信息
     */
    AdsPartner getPartnerById(Long partnerId);
    
    /**
     * 根据合作伙伴Key获取合作伙伴信息（带缓存）
     * 
     * @param partnerKey 合作伙伴Key
     * @return 合作伙伴信息
     */
    AdsPartner getPartnerByKey(String partnerKey);
    
    /**
     * 获取广告信息（带缓存）
     * 
     * @param adsId 广告ID
     * @return 广告信息
     */
    AdsInfo getAdsInfoById(Long adsId);
    
    /**
     * 获取发布者配置（带缓存）
     * 
     * @param offerId 配置ID
     * @return 发布者配置
     */
    AdsPublisherOffer getPublisherOfferById(Long offerId);
    
    /**
     * 获取广告主配置（带缓存）
     * 注意：当前系统中没有独立的AdsAdvertiserOffer，此方法暂时保留接口兼容性
     *
     * @param offerId 配置ID
     * @return 广告主配置
     */
    Object getAdvertiserOfferById(Long offerId);
    
    /**
     * 清除合作伙伴缓存
     * 
     * @param partnerId 合作伙伴ID
     */
    void evictPartnerCache(Long partnerId);
    
    /**
     * 清除合作伙伴Key缓存
     * 
     * @param partnerKey 合作伙伴Key
     */
    void evictPartnerKeyCache(String partnerKey);
    
    /**
     * 清除广告信息缓存
     * 
     * @param adsId 广告ID
     */
    void evictAdsInfoCache(Long adsId);
    
    /**
     * 清除发布者配置缓存
     * 
     * @param offerId 配置ID
     */
    void evictPublisherOfferCache(Long offerId);
    
    /**
     * 清除广告主配置缓存
     * 
     * @param offerId 配置ID
     */
    void evictAdvertiserOfferCache(Long offerId);
    
    /**
     * 清除所有缓存
     */
    void evictAllCache();
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    String getCacheStats();
}
