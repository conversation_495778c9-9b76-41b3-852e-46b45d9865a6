package com.ruoyi.ads.service;

import java.util.List;
import com.ruoyi.ads.domain.AdsPublisherOffer;

/**
 * 广告配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IAdsPublisherOfferService 
{
    /**
     * 查询广告配置
     *
     * @param offerId 广告配置主键
     * @return 广告配置
     */
    public AdsPublisherOffer selectAdsPublisherOfferByOfferId(Long offerId);

    /**
     * 查询广告配置（带关联信息）
     *
     * @param offerId 广告配置主键
     * @return 广告配置（包含广告名称、广告主名称、开发者名称）
     */
    public AdsPublisherOffer selectAdsPublisherOfferWithRelatedByOfferId(Long offerId);

    /**
     * 查询广告配置列表
     * 
     * @param adsPublisherOffer 广告配置
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferList(AdsPublisherOffer adsPublisherOffer);

    /**
     * 新增广告配置
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    public int insertAdsPublisherOffer(AdsPublisherOffer adsPublisherOffer);

    /**
     * 修改广告配置
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    public int updateAdsPublisherOffer(AdsPublisherOffer adsPublisherOffer);

    /**
     * 批量删除广告配置
     *
     * @param offerIds 需要删除的广告配置主键集合
     * @return 结果
     */
    public int deleteAdsPublisherOfferByOfferIds(Long[] offerIds);

    /**
     * 批量删除广告配置
     *
     * @param ids 需要删除的广告配置主键集合字符串
     * @return 结果
     */
    public int deleteAdsPublisherOfferByOfferIds(String ids);

    /**
     * 删除广告配置信息
     * 
     * @param offerId 广告配置主键
     * @return 结果
     */
    public int deleteAdsPublisherOfferByOfferId(Long offerId);

    /**
     * 根据广告ID和开发者ID查询配置
     * 
     * @param adsId 广告ID
     * @param publisherId 开发者ID
     * @return 广告配置
     */
    public AdsPublisherOffer selectAdsPublisherOfferByAdsIdAndPublisherId(Long adsId, Long publisherId);

    /**
     * 根据广告主ID查询配置列表
     * 
     * @param advertiserId 广告主ID
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferByAdvertiserId(Long advertiserId);

    /**
     * 根据开发者ID查询配置列表
     * 
     * @param publisherId 开发者ID
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferByPublisherId(Long publisherId);

    /**
     * 根据广告ID查询配置列表
     * 
     * @param adsId 广告ID
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferByAdsId(Long adsId);

    /**
     * 根据状态查询配置列表
     * 
     * @param status 状态
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferByStatus(String status);

    /**
     * 校验广告和开发者组合是否唯一
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    public String checkAdsPublisherUnique(AdsPublisherOffer adsPublisherOffer);

    /**
     * 查询广告配置列表（包含关联信息）
     * 
     * @param adsPublisherOffer 广告配置
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferListWithRelated(AdsPublisherOffer adsPublisherOffer);

    /**
     * 生成点击链接
     *
     * @param partnerKey 开发者密钥
     * @param offerId 配置ID，如果为null则使用占位符
     * @return 点击链接
     */
    public String generateClickUrl(String partnerKey, Long offerId);

    /**
     * 批量创建广告配置
     * 
     * @param advertiserId 广告主ID
     * @param publisherId 开发者ID
     * @param adsIds 广告ID列表
     * @param adsPublisherOffer 配置模板
     * @return 结果
     */
    public int batchCreateOffers(Long advertiserId, Long publisherId, Long[] adsIds, AdsPublisherOffer adsPublisherOffer);
}
