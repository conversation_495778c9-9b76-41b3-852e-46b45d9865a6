package com.ruoyi.ads.service;

/**
 * 回调HTTP服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface IAdsCallbackHttpService 
{
    /**
     * 发送HTTP回调
     * 
     * @param url 回调URL
     * @param timeout 超时时间（毫秒）
     * @return 回调结果
     */
    CallbackResult sendCallback(String url, int timeout);
    
    /**
     * 发送HTTP回调（使用默认超时时间）
     * 
     * @param url 回调URL
     * @return 回调结果
     */
    CallbackResult sendCallback(String url);
    
    /**
     * 回调结果类
     */
    class CallbackResult {
        private boolean success;
        private int statusCode;
        private String responseBody;
        private String errorMessage;
        private long responseTime;
        
        public CallbackResult() {}
        
        public CallbackResult(boolean success, int statusCode, String responseBody, long responseTime) {
            this.success = success;
            this.statusCode = statusCode;
            this.responseBody = responseBody;
            this.responseTime = responseTime;
        }
        
        public static CallbackResult success(int statusCode, String responseBody, long responseTime) {
            return new CallbackResult(true, statusCode, responseBody, responseTime);
        }
        
        public static CallbackResult failure(String errorMessage, long responseTime) {
            CallbackResult result = new CallbackResult();
            result.success = false;
            result.errorMessage = errorMessage;
            result.responseTime = responseTime;
            return result;
        }
        
        public static CallbackResult failure(int statusCode, String responseBody, long responseTime) {
            CallbackResult result = new CallbackResult();
            result.success = false;
            result.statusCode = statusCode;
            result.responseBody = responseBody;
            result.responseTime = responseTime;
            return result;
        }
        
        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public int getStatusCode() {
            return statusCode;
        }
        
        public void setStatusCode(int statusCode) {
            this.statusCode = statusCode;
        }
        
        public String getResponseBody() {
            return responseBody;
        }
        
        public void setResponseBody(String responseBody) {
            this.responseBody = responseBody;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public long getResponseTime() {
            return responseTime;
        }
        
        public void setResponseTime(long responseTime) {
            this.responseTime = responseTime;
        }
    }
}
