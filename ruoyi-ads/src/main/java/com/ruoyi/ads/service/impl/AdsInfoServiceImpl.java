package com.ruoyi.ads.service.impl;

import com.ruoyi.ads.domain.AdsInfo;
import com.ruoyi.ads.mapper.AdsInfoMapper;
import com.ruoyi.ads.service.IAdsInfoService;
import com.ruoyi.ads.service.IAdsCacheService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.ruoyi.common.utils.ShiroUtils.getLoginName;

/**
 * 广告信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class AdsInfoServiceImpl implements IAdsInfoService 
{
    @Autowired
    private AdsInfoMapper adsInfoMapper;

    @Autowired
    private IAdsCacheService adsCacheService;

    /**
     * 查询广告信息
     * 
     * @param adsId 广告信息主键
     * @return 广告信息
     */
    @Override
    public AdsInfo selectAdsInfoByAdsId(Long adsId)
    {
        return adsInfoMapper.selectAdsInfoByAdsId(adsId);
    }

    /**
     * 查询广告信息列表
     * 
     * @param adsInfo 广告信息
     * @return 广告信息
     */
    @Override
    public List<AdsInfo> selectAdsInfoList(AdsInfo adsInfo)
    {
        return adsInfoMapper.selectAdsInfoList(adsInfo);
    }

    /**
     * 新增广告信息
     * 
     * @param adsInfo 广告信息
     * @return 结果
     */
    @Override
    public int insertAdsInfo(AdsInfo adsInfo)
    {
        // 校验广告名称唯一性
        if (!checkAdsNameUnique(adsInfo).equals("0"))
        {
            throw new ServiceException("新增广告'" + adsInfo.getAdsName() + "'失败，广告名称已存在");
        }
        
        // 校验应用ID唯一性
        if (!checkAppIdUnique(adsInfo).equals("0"))
        {
            throw new ServiceException("新增广告'" + adsInfo.getAdsName() + "'失败，应用ID已存在");
        }
        
        adsInfo.setCreateBy(getLoginName());
        adsInfo.setCreateTime(DateUtils.getNowDate());
        return adsInfoMapper.insertAdsInfo(adsInfo);
    }

    /**
     * 修改广告信息
     * 
     * @param adsInfo 广告信息
     * @return 结果
     */
    @Override
    public int updateAdsInfo(AdsInfo adsInfo)
    {
        // 校验广告名称唯一性
        if (!checkAdsNameUnique(adsInfo).equals("0"))
        {
            throw new ServiceException("修改广告'" + adsInfo.getAdsName() + "'失败，广告名称已存在");
        }
        
        // 校验应用ID唯一性
        if (!checkAppIdUnique(adsInfo).equals("0"))
        {
            throw new ServiceException("修改广告'" + adsInfo.getAdsName() + "'失败，应用ID已存在");
        }
        
        adsInfo.setUpdateBy(getLoginName());
        adsInfo.setUpdateTime(DateUtils.getNowDate());
        int result = adsInfoMapper.updateAdsInfo(adsInfo);

        // 清除缓存
        if (result > 0) {
            adsCacheService.evictAdsInfoCache(adsInfo.getAdsId());
        }

        return result;
    }

    /**
     * 批量删除广告信息
     *
     * @param adsIds 需要删除的广告信息主键
     * @return 结果
     */
    @Override
    public int deleteAdsInfoByAdsIds(Long[] adsIds)
    {
        int result = adsInfoMapper.deleteAdsInfoByAdsIds(adsIds);

        // 清除缓存
        if (result > 0) {
            for (Long adsId : adsIds) {
                adsCacheService.evictAdsInfoCache(adsId);
            }
        }

        return result;
    }

    /**
     * 批量删除广告信息
     *
     * @param ids 需要删除的广告信息主键集合字符串
     * @return 结果
     */
    @Override
    public int deleteAdsInfoByAdsIds(String ids)
    {
        Long[] adsIds = Convert.toLongArray(ids);
        int result = adsInfoMapper.deleteAdsInfoByAdsIds(adsIds);

        // 清除缓存
        if (result > 0) {
            for (Long adsId : adsIds) {
                adsCacheService.evictAdsInfoCache(adsId);
            }
        }

        return result;
    }

    /**
     * 删除广告信息信息
     * 
     * @param adsId 广告信息主键
     * @return 结果
     */
    @Override
    public int deleteAdsInfoByAdsId(Long adsId)
    {
        int result = adsInfoMapper.deleteAdsInfoByAdsId(adsId);

        // 清除缓存
        if (result > 0) {
            adsCacheService.evictAdsInfoCache(adsId);
        }

        return result;
    }

    /**
     * 根据广告主ID查询广告列表
     * 
     * @param advertiserId 广告主ID
     * @return 广告信息集合
     */
    @Override
    public List<AdsInfo> selectAdsInfoByAdvertiserId(Long advertiserId)
    {
        return adsInfoMapper.selectAdsInfoByAdvertiserId(advertiserId);
    }

    /**
     * 根据状态查询广告列表
     * 
     * @param status 状态
     * @return 广告信息集合
     */
    @Override
    public List<AdsInfo> selectAdsInfoByStatus(String status)
    {
        return adsInfoMapper.selectAdsInfoByStatus(status);
    }

    /**
     * 根据应用ID查询广告
     * 
     * @param appId 应用ID
     * @return 广告信息
     */
    @Override
    public AdsInfo selectAdsInfoByAppId(String appId)
    {
        return adsInfoMapper.selectAdsInfoByAppId(appId);
    }

    /**
     * 校验广告名称是否唯一
     * 
     * @param adsInfo 广告信息
     * @return 结果
     */
    @Override
    public String checkAdsNameUnique(AdsInfo adsInfo)
    {
        Long adsId = StringUtils.isNull(adsInfo.getAdsId()) ? -1L : adsInfo.getAdsId();
        AdsInfo info = adsInfoMapper.checkAdsNameUnique(adsInfo);
        if (StringUtils.isNotNull(info) && info.getAdsId().longValue() != adsId.longValue())
        {
            return "1";
        }
        return "0";
    }

    /**
     * 校验应用ID是否唯一
     * 
     * @param adsInfo 广告信息
     * @return 结果
     */
    @Override
    public String checkAppIdUnique(AdsInfo adsInfo)
    {
        Long adsId = StringUtils.isNull(adsInfo.getAdsId()) ? -1L : adsInfo.getAdsId();
        AdsInfo info = adsInfoMapper.checkAppIdUnique(adsInfo);
        if (StringUtils.isNotNull(info) && info.getAdsId().longValue() != adsId.longValue())
        {
            return "1";
        }
        return "0";
    }

    /**
     * 导入广告数据
     * 
     * @param adsInfoList 广告数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importAdsInfo(List<AdsInfo> adsInfoList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(adsInfoList) || adsInfoList.size() == 0)
        {
            throw new ServiceException("导入广告数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AdsInfo adsInfo : adsInfoList)
        {
            try
            {
                // 验证是否存在这个广告
                AdsInfo a = adsInfoMapper.selectAdsInfoByAppId(adsInfo.getAppId());
                if (StringUtils.isNull(a))
                {
                    adsInfo.setCreateBy(operName);
                    adsInfo.setCreateTime(DateUtils.getNowDate());
                    this.insertAdsInfo(adsInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、广告 " + adsInfo.getAdsName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    adsInfo.setAdsId(a.getAdsId());
                    adsInfo.setUpdateBy(operName);
                    adsInfo.setUpdateTime(DateUtils.getNowDate());
                    this.updateAdsInfo(adsInfo);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、广告 " + adsInfo.getAdsName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、广告 " + adsInfo.getAdsName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、广告 " + adsInfo.getAdsName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
