package com.ruoyi.ads.service.impl;

import com.ruoyi.ads.domain.AdsClickRecord;
import com.ruoyi.ads.mapper.AdsClickRecordMapper;
import com.ruoyi.ads.service.IAdsClickRecordService;
import com.ruoyi.ads.util.ClickIdGenerator;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.ruoyi.common.utils.ShiroUtils.getLoginName;

/**
 * 点击记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class AdsClickRecordServiceImpl implements IAdsClickRecordService
{
    private static final Logger logger = LoggerFactory.getLogger(AdsClickRecordServiceImpl.class);

    @Autowired
    private AdsClickRecordMapper adsClickRecordMapper;

    /**
     * 查询点击记录
     * 
     * @param recordId 点击记录主键
     * @return 点击记录
     */
    @Override
    public AdsClickRecord selectAdsClickRecordByRecordId(Long recordId)
    {
        return adsClickRecordMapper.selectAdsClickRecordByRecordId(recordId);
    }

    /**
     * 查询点击记录列表
     * 
     * @param adsClickRecord 点击记录
     * @return 点击记录
     */
    @Override
    public List<AdsClickRecord> selectAdsClickRecordList(AdsClickRecord adsClickRecord)
    {
        return adsClickRecordMapper.selectAdsClickRecordList(adsClickRecord);
    }

    /**
     * 新增点击记录
     * 
     * @param adsClickRecord 点击记录
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAdsClickRecord(AdsClickRecord adsClickRecord)
    {
        // 验证点击ID是否已存在
        if (StringUtils.isNotEmpty(adsClickRecord.getClickId()))
        {
            AdsClickRecord existingRecord = adsClickRecordMapper.selectAdsClickRecordByClickId(adsClickRecord.getClickId());
            if (existingRecord != null)
            {
                throw new ServiceException("点击记录已存在，点击ID：" + adsClickRecord.getClickId());
            }
        }
        
        // 如果没有点击时间，设置为当前时间
        if (adsClickRecord.getClickTime() == null)
        {
            adsClickRecord.setClickTime(new Date());
        }
        
        adsClickRecord.setCreateBy("system");
        adsClickRecord.setCreateTime(DateUtils.getNowDate());
        return adsClickRecordMapper.insertAdsClickRecord(adsClickRecord);
    }

    /**
     * 修改点击记录
     * 
     * @param adsClickRecord 点击记录
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAdsClickRecord(AdsClickRecord adsClickRecord)
    {
        adsClickRecord.setUpdateBy(getLoginName());
        adsClickRecord.setUpdateTime(DateUtils.getNowDate());
        return adsClickRecordMapper.updateAdsClickRecord(adsClickRecord);
    }

    /**
     * 批量删除点击记录
     * 
     * @param recordIds 需要删除的点击记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdsClickRecordByRecordIds(Long[] recordIds)
    {
        return adsClickRecordMapper.deleteAdsClickRecordByRecordIds(recordIds);
    }

    /**
     * 删除点击记录信息
     * 
     * @param recordId 点击记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAdsClickRecordByRecordId(Long recordId)
    {
        return adsClickRecordMapper.deleteAdsClickRecordByRecordId(recordId);
    }

    /**
     * 根据点击ID查询点击记录
     * 
     * @param clickId 点击ID
     * @return 点击记录
     */
    @Override
    public AdsClickRecord selectAdsClickRecordByClickId(String clickId)
    {
        return adsClickRecordMapper.selectAdsClickRecordByClickId(clickId);
    }

    /**
     * 根据配置ID查询点击记录列表
     * 
     * @param offerId 配置ID
     * @return 点击记录集合
     */
    @Override
    public List<AdsClickRecord> selectAdsClickRecordByOfferId(Long offerId)
    {
        return adsClickRecordMapper.selectAdsClickRecordByOfferId(offerId);
    }

    /**
     * 根据开发者ID查询点击记录列表
     * 
     * @param publisherId 开发者ID
     * @return 点击记录集合
     */
    @Override
    public List<AdsClickRecord> selectAdsClickRecordByPublisherId(Long publisherId)
    {
        return adsClickRecordMapper.selectAdsClickRecordByPublisherId(publisherId);
    }

    /**
     * 根据广告主ID查询点击记录列表
     * 
     * @param advertiserId 广告主ID
     * @return 点击记录集合
     */
    @Override
    public List<AdsClickRecord> selectAdsClickRecordByAdvertiserId(Long advertiserId)
    {
        return adsClickRecordMapper.selectAdsClickRecordByAdvertiserId(advertiserId);
    }

    /**
     * 根据广告ID查询点击记录列表
     * 
     * @param adsId 广告ID
     * @return 点击记录集合
     */
    @Override
    public List<AdsClickRecord> selectAdsClickRecordByAdsId(Long adsId)
    {
        return adsClickRecordMapper.selectAdsClickRecordByAdsId(adsId);
    }

    /**
     * 根据时间范围查询点击记录列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 点击记录集合
     */
    @Override
    public List<AdsClickRecord> selectAdsClickRecordByTimeRange(Date startTime, Date endTime)
    {
        return adsClickRecordMapper.selectAdsClickRecordByTimeRange(startTime, endTime);
    }

    /**
     * 查询点击记录列表（包含关联信息）
     * 
     * @param adsClickRecord 点击记录
     * @return 点击记录集合
     */
    @Override
    public List<AdsClickRecord> selectAdsClickRecordListWithRelated(AdsClickRecord adsClickRecord)
    {
        return adsClickRecordMapper.selectAdsClickRecordListWithRelated(adsClickRecord);
    }

    /**
     * 统计点击记录总数
     * 
     * @return 总数
     */
    @Override
    public Long countAdsClickRecord()
    {
        return adsClickRecordMapper.countAdsClickRecord();
    }

    /**
     * 根据条件统计点击记录数
     * 
     * @param adsClickRecord 点击记录
     * @return 统计数
     */
    @Override
    public Long countAdsClickRecordByCondition(AdsClickRecord adsClickRecord)
    {
        return adsClickRecordMapper.countAdsClickRecordByCondition(adsClickRecord);
    }

    /**
     * 根据时间范围统计点击记录数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数
     */
    @Override
    public Long countAdsClickRecordByTimeRange(Date startTime, Date endTime)
    {
        return adsClickRecordMapper.countAdsClickRecordByTimeRange(startTime, endTime);
    }

    /**
     * 异步记录点击数据
     *
     * @param adsClickRecord 点击记录
     */
    @Override
    @Async("adsClickExecutor")
    public void recordClickAsync(AdsClickRecord adsClickRecord)
    {
        String clickId = adsClickRecord.getClickId();
        logger.debug("开始异步记录点击数据: clickId={}", clickId);

        try
        {
            // 验证点击ID有效性
            if (StringUtils.isNotEmpty(clickId) && !ClickIdGenerator.isValidClickId(clickId))
            {
                adsClickRecord.setStatus("1"); // 标记为异常
                adsClickRecord.setRemark("无效的点击ID");
                logger.warn("点击ID无效: clickId={}", clickId);
            }

            // 使用点击ID作为锁，确保同一个点击ID的串行处理
            String lockKey = "async_click_" + clickId;
            synchronized (lockKey.intern()) {
                // 三重检查防止重复插入（数据库层面的最终检查）
                if (StringUtils.isNotEmpty(clickId))
                {
                    AdsClickRecord existingRecord = adsClickRecordMapper.selectAdsClickRecordByClickId(clickId);
                    if (existingRecord != null)
                    {
                        logger.debug("点击记录已存在，跳过异步插入: clickId={}", clickId);
                        return;
                    }
                }

                // 执行插入操作
                int result = insertAdsClickRecord(adsClickRecord);
                if (result > 0) {
                    logger.info("异步记录点击数据成功: clickId={}, recordId={}", clickId, adsClickRecord.getRecordId());
                } else {
                    logger.warn("异步记录点击数据失败，插入结果为0: clickId={}", clickId);
                }
            }
        }
        catch (Exception e)
        {
            // 检查是否为重复键异常
            String errorMsg = e.getMessage();
            if (errorMsg != null && (errorMsg.contains("点击记录已存在") ||
                                   errorMsg.contains("Duplicate entry") ||
                                   errorMsg.contains("duplicate key")))
            {
                logger.debug("点击记录重复，忽略: clickId={}", clickId);
            }
            else
            {
                logger.error("异步记录点击数据失败: clickId={}", clickId, e);
                // 可以在这里添加重试逻辑或者将失败记录写入错误表
            }
        }
    }

    /**
     * 清理过期的点击记录
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    @Override
    @Transactional
    public int cleanExpiredRecords(int days)
    {
        if (days <= 0)
        {
            throw new ServiceException("保留天数必须大于0");
        }
        
        // 计算过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date expiredDate = calendar.getTime();
        
        // 查询过期记录数量
        List<AdsClickRecord> expiredRecords = adsClickRecordMapper.selectAdsClickRecordByTimeRange(null, expiredDate);
        
        if (expiredRecords.isEmpty())
        {
            return 0;
        }
        
        // 批量删除过期记录
        Long[] recordIds = expiredRecords.stream()
                .map(AdsClickRecord::getRecordId)
                .toArray(Long[]::new);
        
        return deleteAdsClickRecordByRecordIds(recordIds);
    }

    // ========== 统计相关方法实现 ==========

    /**
     * 根据时间范围统计点击数
     */
    @Override
    public Long countByDateRange(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId) {
        return adsClickRecordMapper.countByDateRange(startTime, endTime, advertiserId, publisherId);
    }

    /**
     * 根据时间范围获取平均单价
     */
    @Override
    public Double getAvgPayoutByDateRange(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId) {
        return adsClickRecordMapper.getAvgPayoutByDateRange(startTime, endTime, advertiserId, publisherId);
    }

    /**
     * 获取按广告主分布的点击统计
     */
    @Override
    public List<Map<String, Object>> getClickDistributionByAdvertiser(LocalDateTime startTime, LocalDateTime endTime, Integer publisherId) {
        return adsClickRecordMapper.getClickDistributionByAdvertiser(startTime, endTime, publisherId);
    }

    /**
     * 获取按国家分布的点击统计
     */
    @Override
    public List<Map<String, Object>> getClickDistributionByCountry(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId) {
        return adsClickRecordMapper.getClickDistributionByCountry(startTime, endTime, advertiserId, publisherId);
    }

    /**
     * 获取点击统计表格数据
     */
    @Override
    public List<Map<String, Object>> getClickStatisticsTableData(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId) {
        return adsClickRecordMapper.getClickStatisticsTableData(startTime, endTime, advertiserId, publisherId);
    }
}
