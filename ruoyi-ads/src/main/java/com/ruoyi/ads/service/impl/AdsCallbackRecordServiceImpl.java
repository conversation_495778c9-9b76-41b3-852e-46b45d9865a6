package com.ruoyi.ads.service.impl;

import com.ruoyi.ads.domain.AdsCallbackRecord;
import com.ruoyi.ads.domain.AdsClickRecord;
import com.ruoyi.ads.domain.AdsPartner;
import com.ruoyi.ads.mapper.AdsCallbackRecordMapper;
import com.ruoyi.ads.service.*;
import com.ruoyi.ads.util.UrlMacroReplacer;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 回调记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class AdsCallbackRecordServiceImpl implements IAdsCallbackRecordService
{
    private static final Logger logger = LoggerFactory.getLogger(AdsCallbackRecordServiceImpl.class);

    @Autowired
    private AdsCallbackRecordMapper adsCallbackRecordMapper;

    @Autowired
    private IAdsPartnerService adsPartnerService;

    @Autowired
    private IAdsClickRecordService adsClickRecordService;

    @Autowired
    private IAdsCallbackHttpService adsCallbackHttpService;

    @Autowired
    private IAdsCacheService adsCacheService;

    /**
     * 查询回调记录
     * 
     * @param recordId 回调记录主键
     * @return 回调记录
     */
    @Override
    public AdsCallbackRecord selectAdsCallbackRecordByRecordId(Long recordId)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordByRecordId(recordId);
    }

    /**
     * 查询回调记录列表
     * 
     * @param adsCallbackRecord 回调记录
     * @return 回调记录
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordList(AdsCallbackRecord adsCallbackRecord)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordList(adsCallbackRecord);
    }

    /**
     * 新增回调记录
     * 
     * @param adsCallbackRecord 回调记录
     * @return 结果
     */
    @Override
    public int insertAdsCallbackRecord(AdsCallbackRecord adsCallbackRecord)
    {
        // 如果没有回调时间，设置为当前时间
        if (adsCallbackRecord.getCallbackTime() == null)
        {
            adsCallbackRecord.setCallbackTime(new Date());
        }
        
        // 如果没有设置回调状态，默认为待处理
        if (adsCallbackRecord.getCallbackStatus() == null)
        {
            adsCallbackRecord.setCallbackStatus(0);
        }
        
        // 如果没有设置重试次数，默认为0
        if (adsCallbackRecord.getRetryCount() == null)
        {
            adsCallbackRecord.setRetryCount(0);
        }
        
        adsCallbackRecord.setCreateBy("system");
        adsCallbackRecord.setCreateTime(DateUtils.getNowDate());
        return adsCallbackRecordMapper.insertAdsCallbackRecord(adsCallbackRecord);
    }

    /**
     * 修改回调记录
     * 
     * @param adsCallbackRecord 回调记录
     * @return 结果
     */
    @Override
    public int updateAdsCallbackRecord(AdsCallbackRecord adsCallbackRecord)
    {
        adsCallbackRecord.setUpdateBy("system");
        adsCallbackRecord.setUpdateTime(DateUtils.getNowDate());
        return adsCallbackRecordMapper.updateAdsCallbackRecord(adsCallbackRecord);
    }

    /**
     * 批量删除回调记录
     * 
     * @param recordIds 需要删除的回调记录主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAdsCallbackRecordByRecordIds(Long[] recordIds)
    {
        return adsCallbackRecordMapper.deleteAdsCallbackRecordByRecordIds(recordIds);
    }

    /**
     * 删除回调记录信息
     * 
     * @param recordId 回调记录主键
     * @return 结果
     */
    @Override
    public int deleteAdsCallbackRecordByRecordId(Long recordId)
    {
        return adsCallbackRecordMapper.deleteAdsCallbackRecordByRecordId(recordId);
    }

    /**
     * 根据点击ID查询回调记录列表
     * 
     * @param clickId 点击ID
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordByClickId(String clickId)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordByClickId(clickId);
    }

    /**
     * 根据回调类型查询回调记录列表
     * 
     * @param callbackType 回调类型
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordByCallbackType(String callbackType)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordByCallbackType(callbackType);
    }

    /**
     * 根据回调状态查询回调记录列表
     * 
     * @param callbackStatus 回调状态
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordByCallbackStatus(Integer callbackStatus)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordByCallbackStatus(callbackStatus);
    }

    /**
     * 根据开发者ID查询回调记录列表
     * 
     * @param publisherId 开发者ID
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordByPublisherId(Long publisherId)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordByPublisherId(publisherId);
    }

    /**
     * 根据广告主ID查询回调记录列表
     * 
     * @param advertiserId 广告主ID
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordByAdvertiserId(Long advertiserId)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordByAdvertiserId(advertiserId);
    }

    /**
     * 根据广告ID查询回调记录列表
     * 
     * @param adsId 广告ID
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordByAdsId(Long adsId)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordByAdsId(adsId);
    }

    /**
     * 根据时间范围查询回调记录列表
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordByTimeRange(Date startTime, Date endTime)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordByTimeRange(startTime, endTime);
    }

    /**
     * 查询回调记录列表（包含关联信息）
     * 
     * @param adsCallbackRecord 回调记录
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectAdsCallbackRecordListWithRelated(AdsCallbackRecord adsCallbackRecord)
    {
        return adsCallbackRecordMapper.selectAdsCallbackRecordListWithRelated(adsCallbackRecord);
    }

    /**
     * 查询待重试的回调记录列表
     * 
     * @return 回调记录集合
     */
    @Override
    public List<AdsCallbackRecord> selectRetryableCallbackRecords()
    {
        return adsCallbackRecordMapper.selectRetryableCallbackRecords();
    }

    /**
     * 统计回调记录总数
     * 
     * @return 总数
     */
    @Override
    public Long countAdsCallbackRecord()
    {
        return adsCallbackRecordMapper.countAdsCallbackRecord();
    }

    /**
     * 根据条件统计回调记录数
     * 
     * @param adsCallbackRecord 回调记录
     * @return 统计数
     */
    @Override
    public Long countAdsCallbackRecordByCondition(AdsCallbackRecord adsCallbackRecord)
    {
        return adsCallbackRecordMapper.countAdsCallbackRecordByCondition(adsCallbackRecord);
    }

    /**
     * 根据回调状态统计回调记录数
     * 
     * @param callbackStatus 回调状态
     * @return 统计数
     */
    @Override
    public Long countAdsCallbackRecordByCallbackStatus(Integer callbackStatus)
    {
        return adsCallbackRecordMapper.countAdsCallbackRecordByCallbackStatus(callbackStatus);
    }

    /**
     * 根据时间范围统计回调记录数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数
     */
    @Override
    public Long countAdsCallbackRecordByTimeRange(Date startTime, Date endTime)
    {
        return adsCallbackRecordMapper.countAdsCallbackRecordByTimeRange(startTime, endTime);
    }

    /**
     * 异步处理回调
     *
     * @param adsCallbackRecord 回调记录
     */
    @Override
    @Async("adsCallbackExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void processCallbackAsync(AdsCallbackRecord adsCallbackRecord)
    {
        String clickId = adsCallbackRecord.getClickId();
        String callbackType = adsCallbackRecord.getCallbackType();

        logger.info("开始异步处理回调: clickId={}, callbackType={}", clickId, callbackType);

        try
        {
            // 先保存回调记录
            insertAdsCallbackRecord(adsCallbackRecord);

            // 查询点击记录获取开发者信息
            AdsClickRecord clickRecord = adsClickRecordService.selectAdsClickRecordByClickId(clickId);
            if (clickRecord == null) {
                logger.error("点击记录不存在: clickId={}", clickId);
                updateCallbackStatus(adsCallbackRecord.getRecordId(), 2, "点击记录不存在", null);
                return;
            }

            // 查询开发者信息（使用缓存）
            AdsPartner partner = adsCacheService.getPartnerById(clickRecord.getPublisherId());
            if (partner == null) {
                logger.error("开发者不存在: publisherId={}", clickRecord.getPublisherId());
                updateCallbackStatus(adsCallbackRecord.getRecordId(), 2, "开发者不存在", null);
                return;
            }

            // 检查是否启用回调
            if (!partner.isCallbackEnabled()) {
                logger.info("开发者未启用回调: partnerId={}, partnerName={}",
                           partner.getPartnerId(), partner.getPartnerName());
                updateCallbackStatus(adsCallbackRecord.getRecordId(), 1, "开发者未启用回调", null);
                return;
            }

            // 获取回调URL
            String postbackUrl = getPostbackUrl(partner, callbackType);
            if (StringUtils.isEmpty(postbackUrl)) {
                logger.warn("开发者未配置{}回调URL: partnerId={}, partnerName={}",
                           callbackType, partner.getPartnerId(), partner.getPartnerName());
                updateCallbackStatus(adsCallbackRecord.getRecordId(), 1, "未配置回调URL", null);
                return;
            }

            // 构建宏参数并替换URL
            String finalUrl = buildCallbackUrl(postbackUrl, adsCallbackRecord, clickRecord);
            adsCallbackRecord.setCallbackUrl(finalUrl);

            // 发送HTTP回调
            IAdsCallbackHttpService.CallbackResult result = adsCallbackHttpService.sendCallback(finalUrl);

            // 更新回调记录状态
            updateCallbackRecord(adsCallbackRecord, result);

            logger.info("回调处理完成: clickId={}, callbackType={}, success={}, url={}",
                       clickId, callbackType, result.isSuccess(), finalUrl);
        }
        catch (Exception e)
        {
            logger.error("异步处理回调失败: clickId={}, callbackType={}", clickId, callbackType, e);
            if (adsCallbackRecord.getRecordId() != null) {
                updateCallbackStatus(adsCallbackRecord.getRecordId(), 2, "处理异常: " + e.getMessage(), null);
            }
            throw e;
        }
    }

    /**
     * 重试失败的回调
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int retryCallback(Long recordId)
    {
        AdsCallbackRecord record = selectAdsCallbackRecordByRecordId(recordId);
        if (record == null)
        {
            throw new ServiceException("回调记录不存在");
        }
        
        if (record.getCallbackStatus() == 1)
        {
            throw new ServiceException("回调已成功，无需重试");
        }
        
        // 增加重试次数
        record.setRetryCount(record.getRetryCount() + 1);
        record.setCallbackStatus(0); // 重置为待处理状态
        record.setCallbackTime(new Date());
        
        return updateAdsCallbackRecord(record);
    }

    /**
     * 清理过期的回调记录
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    @Override
    @Transactional
    public int cleanExpiredRecords(int days)
    {
        if (days <= 0)
        {
            throw new ServiceException("保留天数必须大于0");
        }
        
        // 计算过期时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date expiredDate = calendar.getTime();
        
        // 查询过期记录数量
        List<AdsCallbackRecord> expiredRecords = adsCallbackRecordMapper.selectAdsCallbackRecordByTimeRange(null, expiredDate);
        
        if (expiredRecords.isEmpty())
        {
            return 0;
        }
        
        // 批量删除过期记录
        Long[] recordIds = expiredRecords.stream()
                .map(AdsCallbackRecord::getRecordId)
                .toArray(Long[]::new);
        
        return deleteAdsCallbackRecordByRecordIds(recordIds);
    }

    /**
     * 获取回调URL
     */
    private String getPostbackUrl(AdsPartner partner, String callbackType) {
        if ("install".equals(callbackType)) {
            return partner.getInstallPostbackUrl();
        } else if ("event".equals(callbackType)) {
            return partner.getEventPostbackUrl();
        }
        return null;
    }

    /**
     * 构建回调URL（替换宏参数）
     */
    private String buildCallbackUrl(String postbackUrl, AdsCallbackRecord callbackRecord, AdsClickRecord clickRecord) {
        Map<String, String> macros = new HashMap<>();

        // 基本参数
        macros.put("click_id", callbackRecord.getClickId());
        macros.put("offer_id", String.valueOf(callbackRecord.getOfferId()));
        macros.put("publisher_id", String.valueOf(callbackRecord.getPublisherId()));
        macros.put("advertiser_id", String.valueOf(callbackRecord.getAdvertiserId()));
        macros.put("ads_id", String.valueOf(callbackRecord.getAdsId()));




        // 点击记录相关参数
        if (clickRecord != null) {
            macros.put("device_id", clickRecord.getDeviceId() != null ? clickRecord.getDeviceId() : "");
            macros.put("ip_address", clickRecord.getIpAddress() != null ? clickRecord.getIpAddress() : "");
            macros.put("sub_param1", clickRecord.getSubParam1() != null ? clickRecord.getSubParam1() : "");
            macros.put("sub_param2", clickRecord.getSubParam2() != null ? clickRecord.getSubParam2() : "");
            macros.put("sub_param3", clickRecord.getSubParam3() != null ? clickRecord.getSubParam3() : "");
            macros.put("channel", clickRecord.getChannel() != null ? clickRecord.getChannel() : "");
            macros.put("country_code", clickRecord.getCountryCode() != null ? clickRecord.getCountryCode() : "");
            macros.put("payout", String.valueOf(clickRecord.getPayout()));
            // 别名宏参数
            macros.put("aff_sub", StringUtils.isNotEmpty(clickRecord.getSubParam1()) ? clickRecord.getSubParam1() : "");
            macros.put("sub2", StringUtils.isNotEmpty(clickRecord.getSubParam2()) ? clickRecord.getSubParam2() : "");
            macros.put("sub3", StringUtils.isNotEmpty(clickRecord.getSubParam3()) ? clickRecord.getSubParam3() : "");
        }

        // 回调特定参数
        macros.put("callback_type", callbackRecord.getCallbackType());
        macros.put("event_name", callbackRecord.getEventName() != null ? callbackRecord.getEventName() : "");
        macros.put("event_value", callbackRecord.getEventValue() != null ? callbackRecord.getEventValue() : "");

        // 时间戳
        macros.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));

        return UrlMacroReplacer.replaceMacros(postbackUrl, macros);
    }

    /**
     * 更新回调记录状态
     */
    private void updateCallbackStatus(Long recordId, Integer status, String response, Integer responseCode) {
        try {
            AdsCallbackRecord updateRecord = new AdsCallbackRecord();
            updateRecord.setRecordId(recordId);
            updateRecord.setCallbackStatus(status);
            updateRecord.setCallbackResponse(response);
            updateRecord.setResponseCode(responseCode);
            updateRecord.setUpdateBy("system");
            updateRecord.setUpdateTime(DateUtils.getNowDate());

            updateAdsCallbackRecord(updateRecord);
        } catch (Exception e) {
            logger.error("更新回调记录状态失败: recordId={}, status={}", recordId, status, e);
        }
    }

    /**
     * 更新回调记录（包含HTTP响应信息）
     */
    private void updateCallbackRecord(AdsCallbackRecord record, IAdsCallbackHttpService.CallbackResult result) {
        try {
            AdsCallbackRecord updateRecord = new AdsCallbackRecord();
            updateRecord.setRecordId(record.getRecordId());
            updateRecord.setCallbackStatus(result.isSuccess() ? 1 : 2);
            updateRecord.setResponseCode(result.getStatusCode());
            updateRecord.setCallbackResponse(result.isSuccess() ? result.getResponseBody() : result.getErrorMessage());
            updateRecord.setUpdateBy("system");
            updateRecord.setUpdateTime(DateUtils.getNowDate());
            updateRecord.setCallbackUrl(record.getCallbackUrl());
            updateAdsCallbackRecord(updateRecord);
        } catch (Exception e) {
            logger.error("更新回调记录失败: recordId={}", record.getRecordId(), e);
        }
    }

    // ========== 统计相关方法实现 ==========

    /**
     * 根据时间范围统计回调数
     */
    @Override
    public Long countByDateRange(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId, String callbackType) {
        return adsCallbackRecordMapper.countByDateRange(startTime, endTime, advertiserId, publisherId, callbackType);
    }

    /**
     * 根据状态和时间范围统计回调数
     */
    @Override
    public Long countByStatusAndDateRange(Integer status, LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId, String callbackType) {
        return adsCallbackRecordMapper.countByStatusAndDateRange(status, startTime, endTime, advertiserId, publisherId, callbackType);
    }

    /**
     * 获取按回调类型分布的统计
     */
    @Override
    public List<Map<String, Object>> getCallbackDistributionByType(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId) {
        return adsCallbackRecordMapper.getCallbackDistributionByType(startTime, endTime, advertiserId, publisherId);
    }

    /**
     * 获取按状态分布的统计
     */
    @Override
    public List<Map<String, Object>> getCallbackDistributionByStatus(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId, String callbackType) {
        return adsCallbackRecordMapper.getCallbackDistributionByStatus(startTime, endTime, advertiserId, publisherId, callbackType);
    }

    /**
     * 获取回调统计表格数据
     */
    @Override
    public List<Map<String, Object>> getCallbackStatisticsTableData(LocalDateTime startTime, LocalDateTime endTime, Integer advertiserId, Integer publisherId, String callbackType) {
        return adsCallbackRecordMapper.getCallbackStatisticsTableData(startTime, endTime, advertiserId, publisherId, callbackType);
    }
}
