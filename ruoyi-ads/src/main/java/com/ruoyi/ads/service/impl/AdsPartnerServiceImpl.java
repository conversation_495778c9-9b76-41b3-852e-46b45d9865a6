package com.ruoyi.ads.service.impl;

import com.ruoyi.ads.domain.AdsPartner;
import com.ruoyi.ads.mapper.AdsPartnerMapper;
import com.ruoyi.ads.service.IAdsPartnerService;
import com.ruoyi.ads.service.IAdsCacheService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

import static com.ruoyi.common.utils.ShiroUtils.getLoginName;

/**
 * 合作伙伴Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class AdsPartnerServiceImpl implements IAdsPartnerService 
{
    @Autowired
    private AdsPartnerMapper adsPartnerMapper;

    @Autowired
    private IAdsCacheService adsCacheService;

    /**
     * 查询合作伙伴
     * 
     * @param partnerId 合作伙伴主键
     * @return 合作伙伴
     */
    @Override
    public AdsPartner selectAdsPartnerByPartnerId(Long partnerId)
    {
        return adsPartnerMapper.selectAdsPartnerByPartnerId(partnerId);
    }

    /**
     * 查询合作伙伴列表
     * 
     * @param adsPartner 合作伙伴
     * @return 合作伙伴
     */
    @Override
    public List<AdsPartner> selectAdsPartnerList(AdsPartner adsPartner)
    {
        return adsPartnerMapper.selectAdsPartnerList(adsPartner);
    }

    /**
     * 新增合作伙伴
     * 
     * @param adsPartner 合作伙伴
     * @return 结果
     */
    @Override
    public int insertAdsPartner(AdsPartner adsPartner)
    {
        // 校验合作伙伴密钥唯一性
        if (!checkPartnerKeyUnique(adsPartner).equals("0"))
        {
            throw new ServiceException("新增合作伙伴'" + adsPartner.getPartnerName() + "'失败，合作伙伴密钥已存在");
        }
        
        // 校验合作伙伴名称唯一性
        if (!checkPartnerNameUnique(adsPartner).equals("0"))
        {
            throw new ServiceException("新增合作伙伴'" + adsPartner.getPartnerName() + "'失败，合作伙伴名称已存在");
        }
        
        // 如果没有提供密钥，则自动生成
        if (StringUtils.isEmpty(adsPartner.getPartnerKey()))
        {
            adsPartner.setPartnerKey(generatePartnerKey());
        }
        
        adsPartner.setCreateBy(getLoginName());
        adsPartner.setCreateTime(DateUtils.getNowDate());
        return adsPartnerMapper.insertAdsPartner(adsPartner);
    }

    /**
     * 修改合作伙伴
     * 
     * @param adsPartner 合作伙伴
     * @return 结果
     */
    @Override
    public int updateAdsPartner(AdsPartner adsPartner)
    {
        // 校验合作伙伴密钥唯一性
        if (!checkPartnerKeyUnique(adsPartner).equals("0"))
        {
            throw new ServiceException("修改合作伙伴'" + adsPartner.getPartnerName() + "'失败，合作伙伴密钥已存在");
        }
        
        // 校验合作伙伴名称唯一性
        if (!checkPartnerNameUnique(adsPartner).equals("0"))
        {
            throw new ServiceException("修改合作伙伴'" + adsPartner.getPartnerName() + "'失败，合作伙伴名称已存在");
        }
        
        adsPartner.setUpdateBy(getLoginName());
        adsPartner.setUpdateTime(DateUtils.getNowDate());
        int result = adsPartnerMapper.updateAdsPartner(adsPartner);

        // 清除缓存
        if (result > 0) {
            adsCacheService.evictPartnerCache(adsPartner.getPartnerId());
            if (StringUtils.isNotEmpty(adsPartner.getPartnerKey())) {
                adsCacheService.evictPartnerKeyCache(adsPartner.getPartnerKey());
            }
        }

        return result;
    }

    /**
     * 批量删除合作伙伴
     * 
     * @param partnerIds 需要删除的合作伙伴主键
     * @return 结果
     */
    @Override
    public int deleteAdsPartnerByPartnerIds(String partnerIds)
    {
        // 先获取要删除的合作伙伴信息，用于清除缓存
        String[] partnerIdArray = Convert.toStrArray(partnerIds);
        for (String partnerIdStr : partnerIdArray) {
            Long partnerId = Long.valueOf(partnerIdStr);
            AdsPartner partner = adsPartnerMapper.selectAdsPartnerByPartnerId(partnerId);
            if (partner != null) {
                adsCacheService.evictPartnerCache(partnerId);
                if (StringUtils.isNotEmpty(partner.getPartnerKey())) {
                    adsCacheService.evictPartnerKeyCache(partner.getPartnerKey());
                }
            }
        }

        return adsPartnerMapper.deleteAdsPartnerByPartnerIds(partnerIdArray);
    }

    /**
     * 删除合作伙伴信息
     * 
     * @param partnerId 合作伙伴主键
     * @return 结果
     */
    @Override
    public int deleteAdsPartnerByPartnerId(Long partnerId)
    {
        // 先获取合作伙伴信息，用于清除缓存
        AdsPartner partner = adsPartnerMapper.selectAdsPartnerByPartnerId(partnerId);
        int result = adsPartnerMapper.deleteAdsPartnerByPartnerId(partnerId);

        // 清除缓存
        if (result > 0 && partner != null) {
            adsCacheService.evictPartnerCache(partnerId);
            if (StringUtils.isNotEmpty(partner.getPartnerKey())) {
                adsCacheService.evictPartnerKeyCache(partner.getPartnerKey());
            }
        }

        return result;
    }

    /**
     * 根据合作伙伴密钥查询合作伙伴
     * 
     * @param partnerKey 合作伙伴密钥
     * @return 合作伙伴
     */
    @Override
    public AdsPartner selectAdsPartnerByPartnerKey(String partnerKey)
    {
        return adsPartnerMapper.selectAdsPartnerByPartnerKey(partnerKey);
    }

    /**
     * 根据合作伙伴类型查询合作伙伴列表
     * 
     * @param partnerType 合作伙伴类型
     * @return 合作伙伴集合
     */
    @Override
    public List<AdsPartner> selectAdsPartnerByPartnerType(Integer partnerType)
    {
        return adsPartnerMapper.selectAdsPartnerByPartnerType(partnerType);
    }

    /**
     * 根据状态查询合作伙伴列表
     * 
     * @param status 状态
     * @return 合作伙伴集合
     */
    @Override
    public List<AdsPartner> selectAdsPartnerByStatus(String status)
    {
        return adsPartnerMapper.selectAdsPartnerByStatus(status);
    }

    /**
     * 校验合作伙伴密钥是否唯一
     * 
     * @param adsPartner 合作伙伴
     * @return 结果
     */
    @Override
    public String checkPartnerKeyUnique(AdsPartner adsPartner)
    {
        Long partnerId = StringUtils.isNull(adsPartner.getPartnerId()) ? -1L : adsPartner.getPartnerId();
        AdsPartner partner = adsPartnerMapper.checkPartnerKeyUnique(adsPartner);
        if (StringUtils.isNotNull(partner) && partner.getPartnerId().longValue() != partnerId.longValue())
        {
            return "1";
        }
        return "0";
    }

    /**
     * 校验合作伙伴名称是否唯一
     * 
     * @param adsPartner 合作伙伴
     * @return 结果
     */
    @Override
    public String checkPartnerNameUnique(AdsPartner adsPartner)
    {
        Long partnerId = StringUtils.isNull(adsPartner.getPartnerId()) ? -1L : adsPartner.getPartnerId();
        AdsPartner partner = adsPartnerMapper.checkPartnerNameUnique(adsPartner);
        if (StringUtils.isNotNull(partner) && partner.getPartnerId().longValue() != partnerId.longValue())
        {
            return "1";
        }
        return "0";
    }

    /**
     * 查询广告主列表（合作伙伴类型为0）
     * 
     * @return 广告主集合
     */
    @Override
    public List<AdsPartner> selectAdvertiserList()
    {
        return adsPartnerMapper.selectAdvertiserList();
    }

    /**
     * 查询开发者列表（合作伙伴类型为1）
     * 
     * @return 开发者集合
     */
    @Override
    public List<AdsPartner> selectPublisherList()
    {
        return adsPartnerMapper.selectPublisherList();
    }

    /**
     * 生成合作伙伴密钥
     * 
     * @return 合作伙伴密钥
     */
    @Override
    public String generatePartnerKey()
    {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 导入合作伙伴数据
     * 
     * @param adsPartnerList 合作伙伴数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importAdsPartner(List<AdsPartner> adsPartnerList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(adsPartnerList) || adsPartnerList.size() == 0)
        {
            throw new ServiceException("导入合作伙伴数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AdsPartner adsPartner : adsPartnerList)
        {
            try
            {
                // 验证是否存在这个合作伙伴
                AdsPartner p = adsPartnerMapper.selectAdsPartnerByPartnerKey(adsPartner.getPartnerKey());
                if (StringUtils.isNull(p))
                {
                    adsPartner.setCreateBy(operName);
                    adsPartner.setCreateTime(DateUtils.getNowDate());
                    this.insertAdsPartner(adsPartner);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、合作伙伴 " + adsPartner.getPartnerName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    adsPartner.setPartnerId(p.getPartnerId());
                    adsPartner.setUpdateBy(operName);
                    adsPartner.setUpdateTime(DateUtils.getNowDate());
                    this.updateAdsPartner(adsPartner);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、合作伙伴 " + adsPartner.getPartnerName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、合作伙伴 " + adsPartner.getPartnerName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、合作伙伴 " + adsPartner.getPartnerName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
