package com.ruoyi.ads.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;

/**
 * 统一API返回格式
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 状态码 */
    private int code;

    /** 返回消息 */
    private String message;

    /** 返回数据 */
    private T data;

    /** 时间戳 */
    private long timestamp;

    /** 请求ID */
    private String requestId;

    public ApiResponse()
    {
        this.timestamp = System.currentTimeMillis();
    }

    public ApiResponse(int code, String message)
    {
        this();
        this.code = code;
        this.message = message;
    }

    public ApiResponse(int code, String message, T data)
    {
        this(code, message);
        this.data = data;
    }

    /**
     * 成功返回
     */
    public static <T> ApiResponse<T> success()
    {
        return new ApiResponse<>(200, "success");
    }

    /**
     * 成功返回（带数据）
     */
    public static <T> ApiResponse<T> success(T data)
    {
        return new ApiResponse<>(200, "success", data);
    }

    /**
     * 成功返回（带消息和数据）
     */
    public static <T> ApiResponse<T> success(String message, T data)
    {
        return new ApiResponse<>(200, message, data);
    }

    /**
     * 失败返回
     */
    public static <T> ApiResponse<T> error()
    {
        return new ApiResponse<>(500, "error");
    }

    /**
     * 失败返回（带消息）
     */
    public static <T> ApiResponse<T> error(String message)
    {
        return new ApiResponse<>(500, message);
    }

    /**
     * 失败返回（带状态码和消息）
     */
    public static <T> ApiResponse<T> error(int code, String message)
    {
        return new ApiResponse<>(code, message);
    }

    /**
     * 参数错误
     */
    public static <T> ApiResponse<T> badRequest(String message)
    {
        return new ApiResponse<>(400, message);
    }

    /**
     * 未授权
     */
    public static <T> ApiResponse<T> unauthorized(String message)
    {
        return new ApiResponse<>(401, message);
    }

    /**
     * 禁止访问
     */
    public static <T> ApiResponse<T> forbidden(String message)
    {
        return new ApiResponse<>(403, message);
    }

    /**
     * 资源不存在
     */
    public static <T> ApiResponse<T> notFound(String message)
    {
        return new ApiResponse<>(404, message);
    }

    /**
     * 方法不允许
     */
    public static <T> ApiResponse<T> methodNotAllowed(String message)
    {
        return new ApiResponse<>(405, message);
    }

    /**
     * 请求过于频繁
     */
    public static <T> ApiResponse<T> tooManyRequests(String message)
    {
        return new ApiResponse<>(429, message);
    }

    /**
     * 设置请求ID
     */
    public ApiResponse<T> requestId(String requestId)
    {
        this.requestId = requestId;
        return this;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess()
    {
        return this.code == 200;
    }

    public int getCode()
    {
        return code;
    }

    public void setCode(int code)
    {
        this.code = code;
    }

    public String getMessage()
    {
        return message;
    }

    public void setMessage(String message)
    {
        this.message = message;
    }

    public T getData()
    {
        return data;
    }

    public void setData(T data)
    {
        this.data = data;
    }

    public long getTimestamp()
    {
        return timestamp;
    }

    public void setTimestamp(long timestamp)
    {
        this.timestamp = timestamp;
    }

    public String getRequestId()
    {
        return requestId;
    }

    public void setRequestId(String requestId)
    {
        this.requestId = requestId;
    }

    @Override
    public String toString()
    {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                ", requestId='" + requestId + '\'' +
                '}';
    }
}
