package com.ruoyi.ads.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 回调记录对象 ads_callback_record
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class AdsCallbackRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 点击ID */
    @Excel(name = "点击ID")
    private String clickId;

    /** 回调类型（install/event） */
    @Excel(name = "回调类型", readConverterExp = "install=安装回调,event=事件回调")
    private String callbackType;

    /** 事件名称 */
    @Excel(name = "事件名称")
    private String eventName;

    /** 事件值 */
    @Excel(name = "事件值")
    private String eventValue;

    /** 配置ID */
    @Excel(name = "配置ID")
    private Long offerId;

    /** 开发者ID */
    @Excel(name = "开发者ID")
    private Long publisherId;

    /** 广告主ID */
    @Excel(name = "广告主ID")
    private Long advertiserId;

    /** 广告ID */
    @Excel(name = "广告ID")
    private Long adsId;

    /** 回调URL */
    @Excel(name = "回调URL")
    private String callbackUrl;

    /** 回调状态（0待处理 1成功 2失败） */
    @Excel(name = "回调状态", readConverterExp = "0=待处理,1=成功,2=失败")
    private Integer callbackStatus;

    /** 回调响应 */
    @Excel(name = "回调响应")
    private String callbackResponse;

    /** 响应状态码 */
    @Excel(name = "响应状态码")
    private Integer responseCode;

    /** 重试次数 */
    @Excel(name = "重试次数")
    private Integer retryCount;

    /** 回调时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回调时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date callbackTime;

    /** 状态（0正常 1异常） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    private String status;

    /** 广告名称（关联查询用） */
    @Excel(name = "广告名称")
    private String adsName;

    /** 广告主名称（关联查询用） */
    @Excel(name = "广告主名称")
    private String advertiserName;

    /** 开发者名称（关联查询用） */
    @Excel(name = "开发者名称")
    private String publisherName;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }
    public void setClickId(String clickId) 
    {
        this.clickId = clickId;
    }

    public String getClickId() 
    {
        return clickId;
    }
    public void setCallbackType(String callbackType) 
    {
        this.callbackType = callbackType;
    }

    public String getCallbackType() 
    {
        return callbackType;
    }
    public void setEventName(String eventName) 
    {
        this.eventName = eventName;
    }

    public String getEventName() 
    {
        return eventName;
    }
    public void setEventValue(String eventValue) 
    {
        this.eventValue = eventValue;
    }

    public String getEventValue() 
    {
        return eventValue;
    }
    public void setOfferId(Long offerId) 
    {
        this.offerId = offerId;
    }

    public Long getOfferId() 
    {
        return offerId;
    }
    public void setPublisherId(Long publisherId) 
    {
        this.publisherId = publisherId;
    }

    public Long getPublisherId() 
    {
        return publisherId;
    }
    public void setAdvertiserId(Long advertiserId) 
    {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserId() 
    {
        return advertiserId;
    }
    public void setAdsId(Long adsId) 
    {
        this.adsId = adsId;
    }

    public Long getAdsId() 
    {
        return adsId;
    }
    public void setCallbackUrl(String callbackUrl) 
    {
        this.callbackUrl = callbackUrl;
    }

    public String getCallbackUrl() 
    {
        return callbackUrl;
    }
    public void setCallbackStatus(Integer callbackStatus) 
    {
        this.callbackStatus = callbackStatus;
    }

    public Integer getCallbackStatus() 
    {
        return callbackStatus;
    }
    public void setCallbackResponse(String callbackResponse) 
    {
        this.callbackResponse = callbackResponse;
    }

    public String getCallbackResponse()
    {
        return callbackResponse;
    }
    public void setResponseCode(Integer responseCode)
    {
        this.responseCode = responseCode;
    }

    public Integer getResponseCode()
    {
        return responseCode;
    }
    public void setRetryCount(Integer retryCount)
    {
        this.retryCount = retryCount;
    }

    public Integer getRetryCount()
    {
        return retryCount;
    }
    public void setCallbackTime(Date callbackTime) 
    {
        this.callbackTime = callbackTime;
    }

    public Date getCallbackTime() 
    {
        return callbackTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getAdsName() {
        return adsName;
    }

    public void setAdsName(String adsName) {
        this.adsName = adsName;
    }

    public String getAdvertiserName() {
        return advertiserName;
    }

    public void setAdvertiserName(String advertiserName) {
        this.advertiserName = advertiserName;
    }

    public String getPublisherName() {
        return publisherName;
    }

    public void setPublisherName(String publisherName) {
        this.publisherName = publisherName;
    }

    /**
     * 获取回调状态名称
     */
    public String getCallbackStatusName() {
        if (callbackStatus == null) {
            return "";
        }
        switch (callbackStatus) {
            case 0: return "待处理";
            case 1: return "成功";
            case 2: return "失败";
            default: return "未知";
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("clickId", getClickId())
            .append("callbackType", getCallbackType())
            .append("eventName", getEventName())
            .append("eventValue", getEventValue())
            .append("offerId", getOfferId())
            .append("publisherId", getPublisherId())
            .append("advertiserId", getAdvertiserId())
            .append("adsId", getAdsId())
            .append("callbackUrl", getCallbackUrl())
            .append("callbackStatus", getCallbackStatus())
            .append("callbackResponse", getCallbackResponse())
            .append("retryCount", getRetryCount())
            .append("callbackTime", getCallbackTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
