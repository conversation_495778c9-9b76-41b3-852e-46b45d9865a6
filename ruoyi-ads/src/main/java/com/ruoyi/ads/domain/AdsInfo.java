package com.ruoyi.ads.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.*;

/**
 * 广告信息对象 ads_info
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class AdsInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 广告ID */
    private Long adsId;

    /** 广告主ID */
    @Excel(name = "广告主ID")
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /** 广告名称 */
    @Excel(name = "广告名称")
    @NotBlank(message = "广告名称不能为空")
    @Size(max = 500, message = "广告名称长度不能超过500个字符")
    private String adsName;

    /** 应用ID */
    @Excel(name = "应用ID")
    @NotBlank(message = "应用ID不能为空")
    @Size(max = 100, message = "应用ID长度不能超过100个字符")
    private String appId;

    /** 包名 */
    @Excel(name = "包名")
    @Size(max = 200, message = "包名长度不能超过200个字符")
    private String packageName;

    /** 应用名称 */
    @Excel(name = "应用名称")
    @Size(max = 200, message = "应用名称长度不能超过200个字符")
    private String appName;

    /** 图标URL */
    @Excel(name = "图标URL")
    @Size(max = 500, message = "图标URL长度不能超过500个字符")
    private String iconUrl;

    /** 广告描述 */
    @Excel(name = "广告描述")
    private String description;

    /** 点击链接 */
    @Excel(name = "点击链接")
    @Size(max = 2000, message = "点击链接长度不能超过2000个字符")
    private String clickUrl;

    /** 预览链接 */
    @Excel(name = "预览链接")
    @Size(max = 2000, message = "预览链接长度不能超过2000个字符")
    private String previewUrl;

    /** 投放国家 */
    @Excel(name = "投放国家")
    @Size(max = 500, message = "投放国家长度不能超过500个字符")
    private String country;

    /** 应用分类 */
    @Excel(name = "应用分类")
    @Size(max = 100, message = "应用分类长度不能超过100个字符")
    private String category;

    /** 点击上限 */
    @Excel(name = "点击上限")
    @Min(value = 0, message = "点击上限不能小于0")
    private Integer clickCap;

    /** 安装上限 */
    @Excel(name = "安装上限")
    @Min(value = 0, message = "安装上限不能小于0")
    private Integer installCap;

    /** 转化上限 */
    @Excel(name = "转化上限")
    @Min(value = 0, message = "转化上限不能小于0")
    private Integer conversionCap;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** KPI要求 */
    @Excel(name = "KPI要求")
    @Size(max = 500, message = "KPI要求长度不能超过500个字符")
    private String kpi;

    /** 转化事件 */
    @Excel(name = "转化事件")
    @Size(max = 200, message = "转化事件长度不能超过200个字符")
    private String conversionEvent;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public void setAdsId(Long adsId) 
    {
        this.adsId = adsId;
    }

    public Long getAdsId() 
    {
        return adsId;
    }
    public void setAdvertiserId(Long advertiserId) 
    {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserId() 
    {
        return advertiserId;
    }
    public void setAdsName(String adsName) 
    {
        this.adsName = adsName;
    }

    public String getAdsName() 
    {
        return adsName;
    }
    public void setAppId(String appId) 
    {
        this.appId = appId;
    }

    public String getAppId() 
    {
        return appId;
    }
    public void setPackageName(String packageName) 
    {
        this.packageName = packageName;
    }

    public String getPackageName() 
    {
        return packageName;
    }
    public void setAppName(String appName) 
    {
        this.appName = appName;
    }

    public String getAppName() 
    {
        return appName;
    }
    public void setIconUrl(String iconUrl) 
    {
        this.iconUrl = iconUrl;
    }

    public String getIconUrl() 
    {
        return iconUrl;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setClickUrl(String clickUrl) 
    {
        this.clickUrl = clickUrl;
    }

    public String getClickUrl() 
    {
        return clickUrl;
    }
    public void setPreviewUrl(String previewUrl) 
    {
        this.previewUrl = previewUrl;
    }

    public String getPreviewUrl() 
    {
        return previewUrl;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setClickCap(Integer clickCap) 
    {
        this.clickCap = clickCap;
    }

    public Integer getClickCap() 
    {
        return clickCap;
    }
    public void setInstallCap(Integer installCap) 
    {
        this.installCap = installCap;
    }

    public Integer getInstallCap() 
    {
        return installCap;
    }
    public void setConversionCap(Integer conversionCap) 
    {
        this.conversionCap = conversionCap;
    }

    public Integer getConversionCap() 
    {
        return conversionCap;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setKpi(String kpi) 
    {
        this.kpi = kpi;
    }

    public String getKpi() 
    {
        return kpi;
    }
    public void setConversionEvent(String conversionEvent) 
    {
        this.conversionEvent = conversionEvent;
    }

    public String getConversionEvent() 
    {
        return conversionEvent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("adsId", getAdsId())
            .append("advertiserId", getAdvertiserId())
            .append("adsName", getAdsName())
            .append("appId", getAppId())
            .append("packageName", getPackageName())
            .append("appName", getAppName())
            .append("iconUrl", getIconUrl())
            .append("description", getDescription())
            .append("clickUrl", getClickUrl())
            .append("previewUrl", getPreviewUrl())
            .append("country", getCountry())
            .append("category", getCategory())
            .append("clickCap", getClickCap())
            .append("installCap", getInstallCap())
            .append("conversionCap", getConversionCap())
            .append("status", getStatus())
            .append("kpi", getKpi())
            .append("conversionEvent", getConversionEvent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
