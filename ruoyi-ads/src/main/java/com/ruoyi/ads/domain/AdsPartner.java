package com.ruoyi.ads.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.*;

/**
 * 合作伙伴对象 ads_partner
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class AdsPartner extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 合作伙伴ID */
    private Long partnerId;

    /** 合作伙伴密钥 */
    @Excel(name = "合作伙伴密钥")
    @NotBlank(message = "合作伙伴密钥不能为空")
    @Size(max = 255, message = "合作伙伴密钥长度不能超过255个字符")
    private String partnerKey;

    /** 合作伙伴名称 */
    @Excel(name = "合作伙伴名称")
    @Size(max = 255, message = "合作伙伴名称长度不能超过255个字符")
    private String partnerName;

    /** 合作伙伴类型（0广告主 1开发者） */
    @Excel(name = "合作伙伴类型", readConverterExp = "0=广告主,1=开发者")
    @NotNull(message = "合作伙伴类型不能为空")
    private Integer partnerType;

    /** 联系人 */
    @Excel(name = "联系人")
    @Size(max = 100, message = "联系人长度不能超过100个字符")
    private String contactPerson;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    private String contactEmail;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;

    /** 安装回调URL */
    @Excel(name = "安装回调URL")
    @Size(max = 500, message = "安装回调URL长度不能超过500个字符")
    private String installPostbackUrl;

    /** 事件回调URL */
    @Excel(name = "事件回调URL")
    @Size(max = 500, message = "事件回调URL长度不能超过500个字符")
    private String eventPostbackUrl;

    /** 是否启用回调（0不启用 1启用） */
    @Excel(name = "是否启用回调", readConverterExp = "0=不启用,1=启用")
    private String enableCallback;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public void setPartnerId(Long partnerId) 
    {
        this.partnerId = partnerId;
    }

    public Long getPartnerId() 
    {
        return partnerId;
    }
    public void setPartnerKey(String partnerKey) 
    {
        this.partnerKey = partnerKey;
    }

    public String getPartnerKey() 
    {
        return partnerKey;
    }
    public void setPartnerName(String partnerName) 
    {
        this.partnerName = partnerName;
    }

    public String getPartnerName() 
    {
        return partnerName;
    }
    public void setPartnerType(Integer partnerType) 
    {
        this.partnerType = partnerType;
    }

    public Integer getPartnerType() 
    {
        return partnerType;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setInstallPostbackUrl(String installPostbackUrl) 
    {
        this.installPostbackUrl = installPostbackUrl;
    }

    public String getInstallPostbackUrl() 
    {
        return installPostbackUrl;
    }
    public void setEventPostbackUrl(String eventPostbackUrl) 
    {
        this.eventPostbackUrl = eventPostbackUrl;
    }

    public String getEventPostbackUrl()
    {
        return eventPostbackUrl;
    }
    public void setEnableCallback(String enableCallback)
    {
        this.enableCallback = enableCallback;
    }

    public String getEnableCallback()
    {
        return enableCallback;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    /**
     * 获取合作伙伴类型名称
     */
    public String getPartnerTypeName() {
        if (partnerType == null) {
            return "";
        }
        return partnerType == 0 ? "广告主" : "开发者";
    }

    /**
     * 判断是否为广告主
     */
    public boolean isAdvertiser() {
        return partnerType != null && partnerType == 0;
    }

    /**
     * 判断是否为开发者
     */
    public boolean isPublisher() {
        return partnerType != null && partnerType == 1;
    }

    /**
     * 判断是否启用回调
     */
    public boolean isCallbackEnabled() {
        return "1".equals(enableCallback);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("partnerId", getPartnerId())
            .append("partnerKey", getPartnerKey())
            .append("partnerName", getPartnerName())
            .append("partnerType", getPartnerType())
            .append("contactPerson", getContactPerson())
            .append("contactEmail", getContactEmail())
            .append("contactPhone", getContactPhone())
            .append("installPostbackUrl", getInstallPostbackUrl())
            .append("eventPostbackUrl", getEventPostbackUrl())
            .append("enableCallback", getEnableCallback())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
