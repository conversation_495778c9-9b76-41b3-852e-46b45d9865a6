package com.ruoyi.ads.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 点击记录对象 ads_click_record
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class AdsClickRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 点击ID */
    @Excel(name = "点击ID")
    private String clickId;

    /** 配置ID */
    @Excel(name = "配置ID")
    private Long offerId;

    /** 开发者ID */
    @Excel(name = "开发者ID")
    private Long publisherId;

    /** 广告主ID */
    @Excel(name = "广告主ID")
    private Long advertiserId;

    /** 广告ID */
    @Excel(name = "广告ID")
    private Long adsId;

    /** 设备广告ID */
    @Excel(name = "设备广告ID")
    private String deviceId;

    /** 客户端IP */
    @Excel(name = "客户端IP")
    private String ipAddress;

    /** User-Agent */
    @Excel(name = "User-Agent")
    private String userAgent;

    /** 国家代码 */
    @Excel(name = "国家代码")
    private String countryCode;

    /** 渠道 */
    @Excel(name = "渠道")
    private String channel;

    /** 附属参数1 */
    @Excel(name = "附属参数1")
    private String subParam1;

    /** 附属参数2 */
    @Excel(name = "附属参数2")
    private String subParam2;

    /** 附属参数3 */
    @Excel(name = "附属参数3")
    private String subParam3;

    /** 目标链接 */
    @Excel(name = "目标链接")
    private String targetUrl;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal payout;

    /** 点击时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "点击时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date clickTime;

    /** 状态（0正常 1异常） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    private String status;

    /** 广告名称（关联查询用） */
    @Excel(name = "广告名称")
    private String adsName;

    /** 广告主名称（关联查询用） */
    @Excel(name = "广告主名称")
    private String advertiserName;

    /** 开发者名称（关联查询用） */
    @Excel(name = "开发者名称")
    private String publisherName;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }
    public void setClickId(String clickId) 
    {
        this.clickId = clickId;
    }

    public String getClickId() 
    {
        return clickId;
    }
    public void setOfferId(Long offerId) 
    {
        this.offerId = offerId;
    }

    public Long getOfferId() 
    {
        return offerId;
    }
    public void setPublisherId(Long publisherId) 
    {
        this.publisherId = publisherId;
    }

    public Long getPublisherId() 
    {
        return publisherId;
    }
    public void setAdvertiserId(Long advertiserId) 
    {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserId() 
    {
        return advertiserId;
    }
    public void setAdsId(Long adsId) 
    {
        this.adsId = adsId;
    }

    public Long getAdsId() 
    {
        return adsId;
    }
    public void setDeviceId(String deviceId) 
    {
        this.deviceId = deviceId;
    }

    public String getDeviceId() 
    {
        return deviceId;
    }
    public void setIpAddress(String ipAddress) 
    {
        this.ipAddress = ipAddress;
    }

    public String getIpAddress() 
    {
        return ipAddress;
    }
    public void setUserAgent(String userAgent) 
    {
        this.userAgent = userAgent;
    }

    public String getUserAgent() 
    {
        return userAgent;
    }
    public void setCountryCode(String countryCode) 
    {
        this.countryCode = countryCode;
    }

    public String getCountryCode() 
    {
        return countryCode;
    }
    public void setChannel(String channel) 
    {
        this.channel = channel;
    }

    public String getChannel() 
    {
        return channel;
    }
    public void setSubParam1(String subParam1) 
    {
        this.subParam1 = subParam1;
    }

    public String getSubParam1() 
    {
        return subParam1;
    }
    public void setSubParam2(String subParam2) 
    {
        this.subParam2 = subParam2;
    }

    public String getSubParam2() 
    {
        return subParam2;
    }
    public void setSubParam3(String subParam3) 
    {
        this.subParam3 = subParam3;
    }

    public String getSubParam3() 
    {
        return subParam3;
    }
    public void setTargetUrl(String targetUrl) 
    {
        this.targetUrl = targetUrl;
    }

    public String getTargetUrl() 
    {
        return targetUrl;
    }
    public void setPayout(BigDecimal payout) 
    {
        this.payout = payout;
    }

    public BigDecimal getPayout() 
    {
        return payout;
    }
    public void setClickTime(Date clickTime) 
    {
        this.clickTime = clickTime;
    }

    public Date getClickTime() 
    {
        return clickTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getAdsName() {
        return adsName;
    }

    public void setAdsName(String adsName) {
        this.adsName = adsName;
    }

    public String getAdvertiserName() {
        return advertiserName;
    }

    public void setAdvertiserName(String advertiserName) {
        this.advertiserName = advertiserName;
    }

    public String getPublisherName() {
        return publisherName;
    }

    public void setPublisherName(String publisherName) {
        this.publisherName = publisherName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("clickId", getClickId())
            .append("offerId", getOfferId())
            .append("publisherId", getPublisherId())
            .append("advertiserId", getAdvertiserId())
            .append("adsId", getAdsId())
            .append("deviceId", getDeviceId())
            .append("ipAddress", getIpAddress())
            .append("userAgent", getUserAgent())
            .append("countryCode", getCountryCode())
            .append("channel", getChannel())
            .append("subParam1", getSubParam1())
            .append("subParam2", getSubParam2())
            .append("subParam3", getSubParam3())
            .append("targetUrl", getTargetUrl())
            .append("payout", getPayout())
            .append("clickTime", getClickTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
