package com.ruoyi.ads.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.*;

/**
 * 广告配置对象 ads_publisher_offer
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class AdsPublisherOffer extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long offerId;

    /** 广告主ID */
    @Excel(name = "广告主ID")
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    /** 开发者ID */
    @Excel(name = "开发者ID")
    @NotNull(message = "开发者ID不能为空")
    private Long publisherId;

    /** 广告ID */
    @Excel(name = "广告ID")
    @NotNull(message = "广告ID不能为空")
    private Long adsId;

    /** 下发单价 */
    @Excel(name = "下发单价")
    @NotNull(message = "下发单价不能为空")
    @DecimalMin(value = "0.001", message = "下发单价必须大于0")
    @DecimalMax(value = "999.999", message = "下发单价不能超过999.999")
    private BigDecimal payout;

    /** 下发地区 */
    @Excel(name = "下发地区")
    @NotBlank(message = "下发地区不能为空")
    @Size(max = 1000, message = "下发地区长度不能超过1000个字符")
    private String country;

    /** 安装上限 */
    @Excel(name = "安装上限")
    @NotNull(message = "安装上限不能为空")
    @Min(value = 1, message = "安装上限必须大于0")
    private Integer installCap;

    /** 每日转化上限 */
    @Excel(name = "每日转化上限")
    @NotNull(message = "每日转化上限不能为空")
    @Min(value = 1, message = "每日转化上限必须大于0")
    private Integer dailyCap;

    /** 点击链接 */
    @Excel(name = "点击链接")
    @NotBlank(message = "点击链接不能为空")
    @Size(max = 2000, message = "点击链接长度不能超过2000个字符")
    private String clickUrl;

    /** 预览链接 */
    @Excel(name = "预览链接")
    @Size(max = 2000, message = "预览链接长度不能超过2000个字符")
    private String previewUrl;

    /** 优先级 */
    @Excel(name = "优先级")
    @Min(value = 1, message = "优先级必须大于0")
    private Integer priority;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 广告名称（关联查询用） */
    @Excel(name = "广告名称")
    private String adsName;

    /** 广告主名称（关联查询用） */
    @Excel(name = "广告主名称")
    private String advertiserName;

    /** KPI */
    @Excel(name = "KPI")
    private String kpi;

    /** 广告名称（关联查询字段） */
    //private String adsName;

    /** 广告主名称（关联查询字段） */
    //private String advertiserName;

    /** 开发者名称（关联查询字段） */
    private String publisherName;

    /** 开发者名称（关联查询用） */
    @Excel(name = "开发者名称")
    //private String publisherName;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public void setOfferId(Long offerId) 
    {
        this.offerId = offerId;
    }

    public Long getOfferId() 
    {
        return offerId;
    }
    public void setAdvertiserId(Long advertiserId) 
    {
        this.advertiserId = advertiserId;
    }

    public Long getAdvertiserId() 
    {
        return advertiserId;
    }
    public void setPublisherId(Long publisherId) 
    {
        this.publisherId = publisherId;
    }

    public Long getPublisherId() 
    {
        return publisherId;
    }
    public void setAdsId(Long adsId) 
    {
        this.adsId = adsId;
    }

    public Long getAdsId() 
    {
        return adsId;
    }
    public void setPayout(BigDecimal payout) 
    {
        this.payout = payout;
    }

    public BigDecimal getPayout() 
    {
        return payout;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setInstallCap(Integer installCap) 
    {
        this.installCap = installCap;
    }

    public Integer getInstallCap() 
    {
        return installCap;
    }
    public void setDailyCap(Integer dailyCap) 
    {
        this.dailyCap = dailyCap;
    }

    public Integer getDailyCap() 
    {
        return dailyCap;
    }
    public void setClickUrl(String clickUrl) 
    {
        this.clickUrl = clickUrl;
    }

    public String getClickUrl() 
    {
        return clickUrl;
    }
    public void setPreviewUrl(String previewUrl) 
    {
        this.previewUrl = previewUrl;
    }

    public String getPreviewUrl() 
    {
        return previewUrl;
    }
    public void setPriority(Integer priority) 
    {
        this.priority = priority;
    }

    public Integer getPriority() 
    {
        return priority;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getAdsName() {
        return adsName;
    }

    public void setAdsName(String adsName) {
        this.adsName = adsName;
    }

    public String getAdvertiserName() {
        return advertiserName;
    }

    public void setAdvertiserName(String advertiserName) {
        this.advertiserName = advertiserName;
    }

    public String getPublisherName() {
        return publisherName;
    }

    public void setPublisherName(String publisherName) {
        this.publisherName = publisherName;
    }

    public String getKpi() {
        return kpi;
    }

    public void setKpi(String kpi) {
        this.kpi = kpi;
    }


    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "";
        }
        return "0".equals(status) ? "正常" : "停用";
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("offerId", getOfferId())
            .append("advertiserId", getAdvertiserId())
            .append("publisherId", getPublisherId())
            .append("adsId", getAdsId())
            .append("payout", getPayout())
            .append("country", getCountry())
            .append("installCap", getInstallCap())
            .append("dailyCap", getDailyCap())
            .append("clickUrl", getClickUrl())
            .append("previewUrl", getPreviewUrl())
            .append("priority", getPriority())
            .append("status", getStatus())
            .append("kpi", getKpi())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
