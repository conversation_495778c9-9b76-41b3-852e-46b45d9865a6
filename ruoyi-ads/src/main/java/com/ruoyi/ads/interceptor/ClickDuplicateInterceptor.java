package com.ruoyi.ads.interceptor;

import com.ruoyi.common.utils.IpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 点击请求防重复拦截器
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Component
public class ClickDuplicateInterceptor implements HandlerInterceptor
{
    private static final Logger logger = LoggerFactory.getLogger(ClickDuplicateInterceptor.class);
    
    // 请求缓存，存储请求键和最后请求时间
    private final Map<String, Long> requestCache = new ConcurrentHashMap<>();
    
    // 重复请求检测时间窗口（毫秒）
    private static final long DUPLICATE_REQUEST_WINDOW = 1000; // 1秒
    
    // 缓存清理间隔（毫秒）
    private static final long CACHE_CLEANUP_INTERVAL = 60000; // 1分钟
    
    // 上次缓存清理时间
    private volatile long lastCleanupTime = System.currentTimeMillis();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        // 只对点击API进行拦截
        String requestURI = request.getRequestURI();
        if (!requestURI.contains("/api/click"))
        {
            return true;
        }

        // 生成请求键
        String requestKey = generateRequestKey(request);
        long currentTime = System.currentTimeMillis();
        
        // 检查是否为重复请求
        Long lastRequestTime = requestCache.get(requestKey);
        if (lastRequestTime != null && (currentTime - lastRequestTime) < DUPLICATE_REQUEST_WINDOW)
        {
            logger.warn("检测到重复点击请求，拒绝处理: requestKey={}, interval={}ms",
                       requestKey, currentTime - lastRequestTime);
            response.setStatus(429); // 429 Too Many Requests
            response.getWriter().write("Too many requests");
            return false;
        }
        
        // 记录请求时间
        requestCache.put(requestKey, currentTime);
        
        // 定期清理过期缓存
        cleanupExpiredCache();
        
        return true;
    }

    /**
     * 生成请求键
     */
    private String generateRequestKey(HttpServletRequest request)
    {
        String offerId = request.getParameter("offer_id");
        String partnerKey = request.getParameter("key");
        String deviceId = request.getParameter("advid");
        String ipAddress = IpUtils.getIpAddr(request);
        
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("click_")
                  .append(offerId != null ? offerId : "")
                  .append("_")
                  .append(partnerKey != null ? partnerKey : "")
                  .append("_")
                  .append(deviceId != null ? deviceId : "")
                  .append("_")
                  .append(ipAddress != null ? ipAddress : "");
        return keyBuilder.toString();
    }

    /**
     * 清理过期的缓存项
     */
    private void cleanupExpiredCache()
    {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCleanupTime > CACHE_CLEANUP_INTERVAL)
        {
            synchronized (this)
            {
                if (currentTime - lastCleanupTime > CACHE_CLEANUP_INTERVAL)
                {
                    requestCache.entrySet().removeIf(entry -> 
                        currentTime - entry.getValue() > DUPLICATE_REQUEST_WINDOW * 10);
                    lastCleanupTime = currentTime;
                    logger.debug("清理过期点击请求缓存完成，当前缓存大小: {}", requestCache.size());
                }
            }
        }
    }
}
