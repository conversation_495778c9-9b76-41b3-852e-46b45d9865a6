package com.ruoyi.ads.interceptor;

import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 点击请求拦截器
 * 用于处理预检请求和防止重复请求
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Component
public class ClickRequestInterceptor implements HandlerInterceptor
{
    private static final Logger logger = LoggerFactory.getLogger(ClickRequestInterceptor.class);
    
    // 请求缓存，用于检测短时间内的重复请求
    private final ConcurrentHashMap<String, Long> requestCache = new ConcurrentHashMap<>();
    
    // 重复请求检测时间窗口（毫秒）
    private static final long DUPLICATE_REQUEST_WINDOW = 1000; // 1秒
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception
    {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        // 只处理点击追踪请求
        if (!requestURI.contains("/api/click")) {
            return true;
        }
        
        logger.debug("拦截点击请求: method={}, uri={}", method, requestURI);
        
        // 处理预检请求
        if ("OPTIONS".equalsIgnoreCase(method) || "HEAD".equalsIgnoreCase(method)) {
            logger.debug("处理预检请求: method={}", method);
            response.setStatus(HttpServletResponse.SC_OK);
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type");
            return false; // 不继续处理
        }
        
        // 只处理GET请求
        if (!"GET".equalsIgnoreCase(method)) {
            logger.warn("不支持的请求方法: method={}", method);
            response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Method not allowed");
            return false;
        }
        
        // 检查重复请求
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String offerId = request.getParameter("offer_id");
        String partnerKey = request.getParameter("key");
        
        String requestKey = generateRequestKey(offerId, partnerKey, clientIp, userAgent);
        long currentTime = System.currentTimeMillis();
        
        Long lastRequestTime = requestCache.get(requestKey);
        if (lastRequestTime != null && (currentTime - lastRequestTime) < DUPLICATE_REQUEST_WINDOW) {
            logger.warn("检测到重复请求: requestKey={}, 时间间隔={}ms", 
                       requestKey, currentTime - lastRequestTime);
            response.sendError(429, "Too many requests");
            return false;
        }
        
        // 记录请求时间
        requestCache.put(requestKey, currentTime);
        
        // 清理过期缓存
        cleanupExpiredCache();
        
        return true;
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        String xRealIp = request.getHeader("X-Real-IP");
        String remoteAddr = request.getRemoteAddr();
        
        if (StringUtils.isNotEmpty(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        if (StringUtils.isNotEmpty(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        return remoteAddr;
    }
    
    /**
     * 生成请求键
     */
    private String generateRequestKey(String offerId, String partnerKey, String ip, String userAgent) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("intercept_")
                  .append(offerId != null ? offerId : "")
                  .append("_")
                  .append(partnerKey != null ? partnerKey : "")
                  .append("_")
                  .append(ip != null ? ip : "")
                  .append("_")
                  .append(userAgent != null ? userAgent.hashCode() : "");
        return keyBuilder.toString();
    }
    
    /**
     * 清理过期的缓存项
     */
    private void cleanupExpiredCache() {
        long currentTime = System.currentTimeMillis();
        requestCache.entrySet().removeIf(entry -> 
            currentTime - entry.getValue() > DUPLICATE_REQUEST_WINDOW * 10); // 保留10倍时间窗口的数据
    }
}
