package com.ruoyi.ads.mapper;

import java.util.List;
import com.ruoyi.ads.domain.AdsInfo;

/**
 * 广告信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface AdsInfoMapper 
{
    /**
     * 查询广告信息
     * 
     * @param adsId 广告信息主键
     * @return 广告信息
     */
    public AdsInfo selectAdsInfoByAdsId(Long adsId);

    /**
     * 查询广告信息列表
     * 
     * @param adsInfo 广告信息
     * @return 广告信息集合
     */
    public List<AdsInfo> selectAdsInfoList(AdsInfo adsInfo);

    /**
     * 新增广告信息
     * 
     * @param adsInfo 广告信息
     * @return 结果
     */
    public int insertAdsInfo(AdsInfo adsInfo);

    /**
     * 修改广告信息
     * 
     * @param adsInfo 广告信息
     * @return 结果
     */
    public int updateAdsInfo(AdsInfo adsInfo);

    /**
     * 删除广告信息
     * 
     * @param adsId 广告信息主键
     * @return 结果
     */
    public int deleteAdsInfoByAdsId(Long adsId);

    /**
     * 批量删除广告信息
     * 
     * @param adsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAdsInfoByAdsIds(Long[] adsIds);

    /**
     * 根据广告主ID查询广告列表
     * 
     * @param advertiserId 广告主ID
     * @return 广告信息集合
     */
    public List<AdsInfo> selectAdsInfoByAdvertiserId(Long advertiserId);

    /**
     * 根据状态查询广告列表
     * 
     * @param status 状态
     * @return 广告信息集合
     */
    public List<AdsInfo> selectAdsInfoByStatus(String status);

    /**
     * 根据应用ID查询广告
     * 
     * @param appId 应用ID
     * @return 广告信息
     */
    public AdsInfo selectAdsInfoByAppId(String appId);

    /**
     * 检查广告名称是否唯一
     * 
     * @param adsInfo 广告信息
     * @return 结果
     */
    public AdsInfo checkAdsNameUnique(AdsInfo adsInfo);

    /**
     * 检查应用ID是否唯一
     * 
     * @param adsInfo 广告信息
     * @return 结果
     */
    public AdsInfo checkAppIdUnique(AdsInfo adsInfo);
}
