package com.ruoyi.ads.mapper;

import java.util.List;
import com.ruoyi.ads.domain.AdsPublisherOffer;

/**
 * 广告配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface AdsPublisherOfferMapper 
{
    /**
     * 查询广告配置
     *
     * @param offerId 广告配置主键
     * @return 广告配置
     */
    public AdsPublisherOffer selectAdsPublisherOfferByOfferId(Long offerId);

    /**
     * 查询广告配置（带关联信息）
     *
     * @param offerId 广告配置主键
     * @return 广告配置（包含广告名称、广告主名称、开发者名称）
     */
    public AdsPublisherOffer selectAdsPublisherOfferWithRelatedByOfferId(Long offerId);

    /**
     * 查询广告配置列表
     * 
     * @param adsPublisherOffer 广告配置
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferList(AdsPublisherOffer adsPublisherOffer);

    /**
     * 新增广告配置
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    public int insertAdsPublisherOffer(AdsPublisherOffer adsPublisherOffer);

    /**
     * 修改广告配置
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    public int updateAdsPublisherOffer(AdsPublisherOffer adsPublisherOffer);

    /**
     * 删除广告配置
     * 
     * @param offerId 广告配置主键
     * @return 结果
     */
    public int deleteAdsPublisherOfferByOfferId(Long offerId);

    /**
     * 批量删除广告配置
     * 
     * @param offerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAdsPublisherOfferByOfferIds(Long[] offerIds);

    /**
     * 根据广告ID和开发者ID查询配置
     * 
     * @param adsId 广告ID
     * @param publisherId 开发者ID
     * @return 广告配置
     */
    public AdsPublisherOffer selectAdsPublisherOfferByAdsIdAndPublisherId(Long adsId, Long publisherId);

    /**
     * 根据广告主ID查询配置列表
     * 
     * @param advertiserId 广告主ID
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferByAdvertiserId(Long advertiserId);

    /**
     * 根据开发者ID查询配置列表
     * 
     * @param publisherId 开发者ID
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferByPublisherId(Long publisherId);

    /**
     * 根据广告ID查询配置列表
     * 
     * @param adsId 广告ID
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferByAdsId(Long adsId);

    /**
     * 根据状态查询配置列表
     * 
     * @param status 状态
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferByStatus(String status);

    /**
     * 检查广告和开发者组合是否唯一
     * 
     * @param adsPublisherOffer 广告配置
     * @return 结果
     */
    public AdsPublisherOffer checkAdsPublisherUnique(AdsPublisherOffer adsPublisherOffer);

    /**
     * 查询广告配置列表（包含关联信息）
     * 
     * @param adsPublisherOffer 广告配置
     * @return 广告配置集合
     */
    public List<AdsPublisherOffer> selectAdsPublisherOfferListWithRelated(AdsPublisherOffer adsPublisherOffer);
}
