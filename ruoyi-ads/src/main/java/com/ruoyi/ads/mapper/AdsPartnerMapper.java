package com.ruoyi.ads.mapper;

import java.util.List;
import com.ruoyi.ads.domain.AdsPartner;

/**
 * 合作伙伴Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface AdsPartnerMapper 
{
    /**
     * 查询合作伙伴
     * 
     * @param partnerId 合作伙伴主键
     * @return 合作伙伴
     */
    public AdsPartner selectAdsPartnerByPartnerId(Long partnerId);

    /**
     * 查询合作伙伴列表
     * 
     * @param adsPartner 合作伙伴
     * @return 合作伙伴集合
     */
    public List<AdsPartner> selectAdsPartnerList(AdsPartner adsPartner);

    /**
     * 新增合作伙伴
     * 
     * @param adsPartner 合作伙伴
     * @return 结果
     */
    public int insertAdsPartner(AdsPartner adsPartner);

    /**
     * 修改合作伙伴
     * 
     * @param adsPartner 合作伙伴
     * @return 结果
     */
    public int updateAdsPartner(AdsPartner adsPartner);

    /**
     * 删除合作伙伴
     * 
     * @param partnerId 合作伙伴主键
     * @return 结果
     */
    public int deleteAdsPartnerByPartnerId(Long partnerId);

    /**
     * 批量删除合作伙伴
     * 
     * @param partnerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAdsPartnerByPartnerIds(String[] partnerIds);

    /**
     * 根据合作伙伴密钥查询合作伙伴
     * 
     * @param partnerKey 合作伙伴密钥
     * @return 合作伙伴
     */
    public AdsPartner selectAdsPartnerByPartnerKey(String partnerKey);

    /**
     * 根据合作伙伴类型查询合作伙伴列表
     * 
     * @param partnerType 合作伙伴类型
     * @return 合作伙伴集合
     */
    public List<AdsPartner> selectAdsPartnerByPartnerType(Integer partnerType);

    /**
     * 根据状态查询合作伙伴列表
     * 
     * @param status 状态
     * @return 合作伙伴集合
     */
    public List<AdsPartner> selectAdsPartnerByStatus(String status);

    /**
     * 检查合作伙伴密钥是否唯一
     * 
     * @param adsPartner 合作伙伴
     * @return 结果
     */
    public AdsPartner checkPartnerKeyUnique(AdsPartner adsPartner);

    /**
     * 检查合作伙伴名称是否唯一
     * 
     * @param adsPartner 合作伙伴
     * @return 结果
     */
    public AdsPartner checkPartnerNameUnique(AdsPartner adsPartner);

    /**
     * 查询广告主列表（合作伙伴类型为0）
     * 
     * @return 广告主集合
     */
    public List<AdsPartner> selectAdvertiserList();

    /**
     * 查询开发者列表（合作伙伴类型为1）
     * 
     * @return 开发者集合
     */
    public List<AdsPartner> selectPublisherList();
}
