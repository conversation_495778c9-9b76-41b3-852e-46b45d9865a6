package com.ruoyi.ads.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * API请求日志记录切面
 * 用于记录AdsApiController中所有接口的请求和响应信息
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Aspect
@Component
public class ApiRequestLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(ApiRequestLogAspect.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 定义切点：拦截AdsApiController中的所有公共方法
     */
    @Pointcut("execution(public * com.ruoyi.ads.controller.AdsApiController.*(..))")
    public void apiControllerMethods() {}

    /**
     * 环绕通知：记录请求和响应信息
     */
    @Around("apiControllerMethods()")
    public Object logApiRequest(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        
        // 记录请求开始日志
        logRequestStart(joinPoint, request);
        
        Object result = null;
        Throwable exception = null;
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            // 记录请求结束日志
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            logRequestEnd(joinPoint, request, result, exception, duration);
        }
    }

    /**
     * 记录请求开始日志
     */
    private void logRequestStart(JoinPoint joinPoint, HttpServletRequest request) {
        try {
            String methodName = joinPoint.getSignature().getName();
            String className = joinPoint.getTarget().getClass().getSimpleName();
            String requestURI = request.getRequestURI();
            String method = request.getMethod();
            String clientIP = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            
            // 获取请求参数
            Map<String, Object> requestParams = getRequestParameters(request, joinPoint);
            
            logger.info("=== API请求开始 === 类名: {}, 方法: {}, 请求路径: {}, HTTP方法: {}, 客户端IP: {}, User-Agent: {}, 请求参数: {}", 
                       className, methodName, requestURI, method, clientIP, userAgent, 
                       formatParameters(requestParams));
                       
        } catch (Exception e) {
            logger.error("记录API请求开始日志时发生错误", e);
        }
    }

    /**
     * 记录请求结束日志
     */
    private void logRequestEnd(JoinPoint joinPoint, HttpServletRequest request, Object result, 
                              Throwable exception, long duration) {
        try {
            String methodName = joinPoint.getSignature().getName();
            String className = joinPoint.getTarget().getClass().getSimpleName();
            String requestURI = request.getRequestURI();
            String status = exception == null ? "成功" : "失败";
            
            if (exception == null) {
                // 成功情况
                String responseData = formatResponse(result);
                logger.info("=== API请求结束 === 类名: {}, 方法: {}, 请求路径: {}, 处理状态: {}, 耗时: {}ms, 响应数据: {}", 
                           className, methodName, requestURI, status, duration, responseData);
            } else {
                // 失败情况
                logger.error("=== API请求结束 === 类名: {}, 方法: {}, 请求路径: {}, 处理状态: {}, 耗时: {}ms, 异常信息: {}", 
                            className, methodName, requestURI, status, duration, exception.getMessage(), exception);
            }
            
        } catch (Exception e) {
            logger.error("记录API请求结束日志时发生错误", e);
        }
    }

    /**
     * 获取请求参数
     */
    private Map<String, Object> getRequestParameters(HttpServletRequest request, JoinPoint joinPoint) {
        Map<String, Object> params = new HashMap<>();
        
        try {
            // 获取URL参数
            Enumeration<String> parameterNames = request.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String paramName = parameterNames.nextElement();
                String[] paramValues = request.getParameterValues(paramName);
                if (paramValues.length == 1) {
                    params.put(paramName, paramValues[0]);
                } else {
                    params.put(paramName, Arrays.asList(paramValues));
                }
            }
            
            // 获取方法参数名和值
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                for (int i = 0; i < args.length; i++) {
                    if (args[i] != null && !(args[i] instanceof HttpServletRequest) && 
                        !(args[i] instanceof javax.servlet.http.HttpServletResponse)) {
                        params.put("arg" + i, args[i].toString());
                    }
                }
            }
            
        } catch (Exception e) {
            logger.warn("获取请求参数时发生错误: {}", e.getMessage());
        }
        
        return params;
    }

    /**
     * 格式化参数输出
     */
    private String formatParameters(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "无";
        }
        
        try {
            return objectMapper.writeValueAsString(params);
        } catch (Exception e) {
            return params.toString();
        }
    }

    /**
     * 格式化响应数据
     */
    private String formatResponse(Object result) {
        if (result == null) {
            return "null";
        }
        
        try {
            // 对于void方法或者重定向，不记录响应内容
            if (result instanceof Void) {
                return "void";
            }
            
            String resultStr = objectMapper.writeValueAsString(result);
            // 限制响应数据长度，避免日志过长
            if (resultStr.length() > 1000) {
                return resultStr.substring(0, 1000) + "...(truncated)";
            }
            return resultStr;
        } catch (Exception e) {
            return result.toString();
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        String xRealIp = request.getHeader("X-Real-IP");
        String remoteAddr = request.getRemoteAddr();

        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        return remoteAddr;
    }
}
