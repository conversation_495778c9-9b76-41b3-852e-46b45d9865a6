package com.ruoyi.ads.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import com.ruoyi.common.utils.StringUtils;

/**
 * 点击ID生成器
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class ClickIdGenerator {

    /**
     * 生成点击ID
     * 
     * @param offerId 配置ID
     * @param deviceId 设备ID
     * @param partnerKey 合作伙伴密钥
     * @param subParam1 附属参数1
     * @param subParam2 附属参数2
     * @param subParam3 附属参数3
     * @param channel 渠道
     * @param ipAddress IP地址
     * @param userAgent User-Agent
     * @param publisherId 开发者ID
     * @return 点击ID
     */
    public static String generateClickId(Long offerId, String deviceId, String partnerKey,
                                       String subParam1, String subParam2, String subParam3,
                                       String channel, String ipAddress, String userAgent, String publisherId) {
        
        // 构建原始数据
        StringBuilder rawData = new StringBuilder();
        rawData.append("offer_id=").append(offerId != null ? offerId : "");
        rawData.append("&device_id=").append(StringUtils.isNotEmpty(deviceId) ? deviceId : "");
        rawData.append("&partner_key=").append(StringUtils.isNotEmpty(partnerKey) ? partnerKey : "");
//        rawData.append("&sub_param1=").append(StringUtils.isNotEmpty(subParam1) ? subParam1 : "");
//        rawData.append("&sub_param2=").append(StringUtils.isNotEmpty(subParam2) ? subParam2 : "");
//        rawData.append("&sub_param3=").append(StringUtils.isNotEmpty(subParam3) ? subParam3 : "");
        rawData.append("&channel=").append(StringUtils.isNotEmpty(channel) ? channel : "");
        rawData.append("&ip_address=").append(StringUtils.isNotEmpty(ipAddress) ? ipAddress : "");
//        rawData.append("&user_agent=").append(StringUtils.isNotEmpty(userAgent) ? userAgent : "");
        rawData.append("&publisher_id=").append(StringUtils.isNotEmpty(publisherId) ? publisherId : "");
        rawData.append("&timestamp=").append(System.currentTimeMillis());
        
        // 使用Base64编码
        return Base64.getEncoder().encodeToString(rawData.toString().getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 解析点击ID
     * 
     * @param clickId 点击ID
     * @return 解析后的参数Map
     */
    public static Map<String, String> parseClickId(String clickId) {
        Map<String, String> params = new HashMap<>();
        
        try {
            // Base64解码
            String decodedData = new String(Base64.getDecoder().decode(clickId), StandardCharsets.UTF_8);
            
            // 解析参数
            String[] pairs = decodedData.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    params.put(keyValue[0], keyValue[1]);
                }
            }
        } catch (Exception e) {
            // 解析失败，返回空Map
        }
        
        return params;
    }

    /**
     * 验证点击ID是否有效
     * 
     * @param clickId 点击ID
     * @return 是否有效
     */
    public static boolean isValidClickId(String clickId) {
        if (StringUtils.isEmpty(clickId)) {
            return false;
        }
        
        try {
            Map<String, String> params = parseClickId(clickId);
            return params.containsKey("offer_id") && params.containsKey("timestamp");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成MD5哈希
     * 
     * @param input 输入字符串
     * @return MD5哈希值
     */
    private static String generateMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not available", e);
        }
    }

    /**
     * 生成短点击ID（用于URL缩短）
     * 
     * @param offerId 配置ID
     * @param deviceId 设备ID
     * @param partnerKey 合作伙伴密钥
     * @return 短点击ID
     */
    public static String generateShortClickId(Long offerId, String deviceId, String partnerKey) {
        String rawData = offerId + "|" + deviceId + "|" + partnerKey + "|" + System.currentTimeMillis();
        String hash = generateMD5(rawData);
        return hash.substring(0, 16); // 取前16位
    }
}
