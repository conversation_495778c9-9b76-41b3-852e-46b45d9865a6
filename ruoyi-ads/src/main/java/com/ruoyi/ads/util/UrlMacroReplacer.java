package com.ruoyi.ads.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.ruoyi.common.utils.StringUtils;

/**
 * URL宏替换工具类
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class UrlMacroReplacer {

    // 宏参数模式
    private static final Pattern MACRO_PATTERN = Pattern.compile("\\{([^}]+)\\}");

    /**
     * 构建宏参数Map
     * 
     * @param clickId 点击ID
     * @param partnerKey 合作伙伴密钥
     * @param deviceId 设备ID
     * @param ipAddress IP地址
     * @param userAgent User-Agent
     * @param subParam1 附属参数1
     * @param subParam2 附属参数2
     * @param subParam3 附属参数3
     * @param channel 渠道
     * @return 宏参数Map
     */
    public static Map<String, String> buildMacros(String clickId, String partnerKey, String deviceId,
                                                String ipAddress, String userAgent, String subParam1,
                                                String subParam2, String subParam3, String channel) {
        Map<String, String> macros = new HashMap<>();
        
        // 基础宏参数
        macros.put("click_id", StringUtils.isNotEmpty(clickId) ? clickId : "");
        macros.put("partner_key", StringUtils.isNotEmpty(partnerKey) ? partnerKey : "");
        macros.put("device_id", StringUtils.isNotEmpty(deviceId) ? deviceId : "");
        macros.put("advid", StringUtils.isNotEmpty(deviceId) ? deviceId : ""); // 别名
        macros.put("ip_address", StringUtils.isNotEmpty(ipAddress) ? ipAddress : "");
        macros.put("user_agent", StringUtils.isNotEmpty(userAgent) ? userAgent : "");
        macros.put("sub_param1", StringUtils.isNotEmpty(subParam1) ? subParam1 : "");
        macros.put("sub_param2", StringUtils.isNotEmpty(subParam2) ? subParam2 : "");
        macros.put("sub_param3", StringUtils.isNotEmpty(subParam3) ? subParam3 : "");
        macros.put("channel", StringUtils.isNotEmpty(channel) ? channel : "");
        
        // 别名宏参数
        macros.put("aff_sub", StringUtils.isNotEmpty(subParam1) ? subParam1 : "");
        macros.put("sub2", StringUtils.isNotEmpty(subParam2) ? subParam2 : "");
        macros.put("sub3", StringUtils.isNotEmpty(subParam3) ? subParam3 : "");
        
        // 时间戳宏参数
        macros.put("timestamp", String.valueOf(System.currentTimeMillis()));
        macros.put("timestamp_sec", String.valueOf(System.currentTimeMillis() / 1000));
        
        // URL编码版本
        macros.put("click_id_encoded", urlEncode(clickId));
        macros.put("device_id_encoded", urlEncode(deviceId));
        macros.put("user_agent_encoded", urlEncode(userAgent));
        
        return macros;
    }

    /**
     * 替换URL中的宏参数
     * 
     * @param url 原始URL
     * @param macros 宏参数Map
     * @return 替换后的URL
     */
    public static String replaceMacros(String url, Map<String, String> macros) {
        if (StringUtils.isEmpty(url) || macros == null || macros.isEmpty()) {
            return url;
        }
        
        Matcher matcher = MACRO_PATTERN.matcher(url);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String macroName = matcher.group(1);
            String macroValue = macros.getOrDefault(macroName, "");
            matcher.appendReplacement(result, Matcher.quoteReplacement(macroValue));
        }
        
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * URL编码
     * 
     * @param value 原始值
     * @return 编码后的值
     */
    private static String urlEncode(String value) {
        if (StringUtils.isEmpty(value)) {
            return "";
        }
        
        try {
            return URLEncoder.encode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            return value;
        }
    }

    /**
     * 获取支持的宏参数列表
     * 
     * @return 宏参数列表
     */
    public static String[] getSupportedMacros() {
        return new String[]{
            "click_id", "partner_key", "device_id", "advid", "ip_address", "user_agent",
            "sub_param1", "sub_param2", "sub_param3", "channel", "aff_sub", "sub2", "sub3",
            "timestamp", "timestamp_sec", "click_id_encoded", "device_id_encoded", "user_agent_encoded"
        };
    }

    /**
     * 验证URL中的宏参数是否有效
     * 
     * @param url URL
     * @return 验证结果
     */
    public static boolean validateMacros(String url) {
        if (StringUtils.isEmpty(url)) {
            return true;
        }
        
        Matcher matcher = MACRO_PATTERN.matcher(url);
        String[] supportedMacros = getSupportedMacros();
        
        while (matcher.find()) {
            String macroName = matcher.group(1);
            boolean isSupported = false;
            
            for (String supportedMacro : supportedMacros) {
                if (supportedMacro.equals(macroName)) {
                    isSupported = true;
                    break;
                }
            }
            
            if (!isSupported) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 提取URL中的宏参数
     * 
     * @param url URL
     * @return 宏参数列表
     */
    public static String[] extractMacros(String url) {
        if (StringUtils.isEmpty(url)) {
            return new String[0];
        }
        
        Matcher matcher = MACRO_PATTERN.matcher(url);
        java.util.List<String> macros = new java.util.ArrayList<>();
        
        while (matcher.find()) {
            String macroName = matcher.group(1);
            if (!macros.contains(macroName)) {
                macros.add(macroName);
            }
        }
        
        return macros.toArray(new String[0]);
    }
}
