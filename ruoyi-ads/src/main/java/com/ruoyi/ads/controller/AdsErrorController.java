package com.ruoyi.ads.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import com.ruoyi.common.annotation.Anonymous;

/**
 * 广告错误页面控制器
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Controller
@RequestMapping("/ads/error")
public class AdsErrorController
{
    private static final Logger logger = LoggerFactory.getLogger(AdsErrorController.class);

    @Value("${ruoyi.profile:prod}")
    private String profile;

    /**
     * 点击追踪错误页面
     */
    @GetMapping("/click")
    @Anonymous
    public String clickError(@RequestParam(value = "code", defaultValue = "400") String errorCode,
                           @RequestParam(value = "msg", defaultValue = "请求处理失败") String errorMessage,
                           @RequestParam(value = "offer_id", required = false) String offerId,
                           @RequestParam(value = "partner_key", required = false) String partnerKey,
                           @RequestParam(value = "request_id", required = false) String requestId,
                           Model model)
    {
        logger.warn("点击追踪错误页面访问: code={}, msg={}, offerId={}, partnerKey={}, requestId={}", 
                   errorCode, errorMessage, offerId, partnerKey, requestId);

        model.addAttribute("errorCode", errorCode);
        model.addAttribute("errorMessage", errorMessage);
        model.addAttribute("offerId", offerId);
        model.addAttribute("partnerKey", partnerKey);
        model.addAttribute("requestId", requestId);
        
        // 在开发环境显示更多调试信息
        if ("dev".equals(profile) || "test".equals(profile)) {
            model.addAttribute("debugMode", true);
        }

        return "error/click-error";
    }

    /**
     * 通用API错误页面
     */
    @GetMapping("/api")
    @Anonymous
    public String apiError(@RequestParam(value = "code", defaultValue = "500") String errorCode,
                         @RequestParam(value = "msg", defaultValue = "服务器内部错误") String errorMessage,
                         @RequestParam(value = "request_id", required = false) String requestId,
                         Model model)
    {
        logger.warn("API错误页面访问: code={}, msg={}, requestId={}", errorCode, errorMessage, requestId);

        model.addAttribute("errorCode", errorCode);
        model.addAttribute("errorMessage", errorMessage);
        model.addAttribute("requestId", requestId);
        
        // 在开发环境显示更多调试信息
        if ("dev".equals(profile) || "test".equals(profile)) {
            model.addAttribute("debugMode", true);
        }

        return "error/api-error";
    }
}
