package com.ruoyi.ads.controller;

import com.ruoyi.ads.domain.AdsClickRecord;
import com.ruoyi.ads.service.IAdsClickRecordService;
import com.ruoyi.ads.service.IAdsPartnerService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 点击记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/ads/click/record")
public class AdsClickRecordController extends BaseController
{
    @Autowired
    private IAdsClickRecordService adsClickRecordService;
    
    @Autowired
    private IAdsPartnerService adsPartnerService;

    /**
     * 查询点击记录列表
     */
    @RequiresPermissions("ads:click:records")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AdsClickRecord adsClickRecord)
    {
        startPage();
        List<AdsClickRecord> list = adsClickRecordService.selectAdsClickRecordListWithRelated(adsClickRecord);
        return getDataTable(list);
    }

    /**
     * 导出点击记录列表
     */
    @RequiresPermissions("ads:click:records")
    @Log(title = "点击记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdsClickRecord adsClickRecord)
    {
        List<AdsClickRecord> list = adsClickRecordService.selectAdsClickRecordListWithRelated(adsClickRecord);
        ExcelUtil<AdsClickRecord> util = new ExcelUtil<AdsClickRecord>(AdsClickRecord.class);
        util.exportExcel(response, list, "点击记录数据");
    }

    /**
     * 获取点击记录详细信息
     */
    @RequiresPermissions("ads:click:records")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return AjaxResult.success(adsClickRecordService.selectAdsClickRecordByRecordId(recordId));
    }

    /**
     * 新增点击记录
     */
    @RequiresPermissions("ads:click:records")
    @Log(title = "点击记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdsClickRecord adsClickRecord)
    {
        return toAjax(adsClickRecordService.insertAdsClickRecord(adsClickRecord));
    }

    /**
     * 修改点击记录
     */
    @RequiresPermissions("ads:click:records")
    @Log(title = "点击记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdsClickRecord adsClickRecord)
    {
        return toAjax(adsClickRecordService.updateAdsClickRecord(adsClickRecord));
    }

    /**
     * 获取点击记录详细信息
     */
    //@RequiresPermissions("ads:click:records")
    //@GetMapping(value = "/{recordId}")
    //public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    //{
    //    return AjaxResult.success(adsClickRecordService.selectAdsClickRecordByRecordId(recordId));
    //}

    /**
     * 删除点击记录
     */
    @RequiresPermissions("ads:click:records")
    @Log(title = "点击记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(adsClickRecordService.deleteAdsClickRecordByRecordIds(recordIds));
    }

    /**
     * 根据点击ID查询点击记录
     */
    @RequiresPermissions("ads:click:records")
    @GetMapping("/byClickId/{clickId}")
    public AjaxResult getByClickId(@PathVariable String clickId)
    {
        return AjaxResult.success(adsClickRecordService.selectAdsClickRecordByClickId(clickId));
    }

    /**
     * 根据开发者ID查询点击记录
     */
    @RequiresPermissions("ads:click:records")
    @GetMapping("/byPublisher/{publisherId}")
    public AjaxResult getByPublisher(@PathVariable Long publisherId)
    {
        return AjaxResult.success(adsClickRecordService.selectAdsClickRecordByPublisherId(publisherId));
    }

    /**
     * 根据广告主ID查询点击记录
     */
    @RequiresPermissions("ads:click:records")
    @GetMapping("/byAdvertiser/{advertiserId}")
    public AjaxResult getByAdvertiser(@PathVariable Long advertiserId)
    {
        return AjaxResult.success(adsClickRecordService.selectAdsClickRecordByAdvertiserId(advertiserId));
    }

    /**
     * 根据广告ID查询点击记录
     */
    @RequiresPermissions("ads:click:records")
    @GetMapping("/byAds/{adsId}")
    public AjaxResult getByAds(@PathVariable Long adsId)
    {
        return AjaxResult.success(adsClickRecordService.selectAdsClickRecordByAdsId(adsId));
    }

    /**
     * 获取点击记录统计
     */
    @RequiresPermissions("ads:click:records")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Long totalClicks = adsClickRecordService.countAdsClickRecord();
        return AjaxResult.success().put("totalClicks", totalClicks);
    }

    /**
     * 获取广告主列表
     */
    @GetMapping("/advertiserList")
    public AjaxResult getAdvertiserList()
    {
        return AjaxResult.success(adsPartnerService.selectAdvertiserList());
    }

    /**
     * 获取开发者列表
     */
    @GetMapping("/publisherList")
    public AjaxResult getPublisherList()
    {
        return AjaxResult.success(adsPartnerService.selectPublisherList());
    }

    /**
     * 清理过期记录
     */
    @RequiresPermissions("ads:click:records")
    @Log(title = "点击记录", businessType = BusinessType.CLEAN)
    @PostMapping("/clean/{days}")
    public AjaxResult cleanExpiredRecords(@PathVariable int days)
    {
        int cleanedCount = adsClickRecordService.cleanExpiredRecords(days);
        return AjaxResult.success("成功清理 " + cleanedCount + " 条过期记录");
    }
}
