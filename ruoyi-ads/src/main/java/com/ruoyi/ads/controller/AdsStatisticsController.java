package com.ruoyi.ads.controller;

import com.ruoyi.ads.service.IAdsCallbackRecordService;
import com.ruoyi.ads.service.IAdsClickRecordService;
import com.ruoyi.ads.service.IAdsInfoService;
import com.ruoyi.ads.service.IAdsPartnerService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 广告统计Controller
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@RestController
@RequestMapping("/ads/statistics")
public class AdsStatisticsController extends BaseController
{
    @Autowired
    private IAdsClickRecordService adsClickRecordService;
    
    @Autowired
    private IAdsCallbackRecordService adsCallbackRecordService;
    
    @Autowired
    private IAdsPartnerService adsPartnerService;
    
    @Autowired
    private IAdsInfoService adsInfoService;

    /**
     * 获取点击统计数据
     */
    @RequiresPermissions("ads:click:statistics")
    @GetMapping("/click/data")
    public AjaxResult getClickStatistics(
            @RequestParam(required = false) Integer advertiserId,
            @RequestParam(required = false) Integer publisherId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime)
    {
        try {
            // 设置默认时间范围（最近7天）
            if (beginTime == null) {
                beginTime = LocalDate.now().minusDays(6);
            }
            if (endTime == null) {
                endTime = LocalDate.now();
            }

            Map<String, Object> result = new HashMap<>();
            
            // 获取统计概览数据
            Map<String, Object> overview = getClickOverview(advertiserId, publisherId, beginTime, endTime);
            result.put("overview", overview);
            
            // 获取趋势数据
            List<Map<String, Object>> trendData = getClickTrendData(advertiserId, publisherId, beginTime, endTime);
            result.put("trendData", trendData);
            
            // 获取分布数据
            Map<String, Object> distribution = getClickDistribution(advertiserId, publisherId, beginTime, endTime);
            result.put("distribution", distribution);
            
            // 获取详细统计表格数据
            startPage();
            List<Map<String, Object>> tableData = getClickTableData(advertiserId, publisherId, beginTime, endTime);
            result.put("tableData", getDataTable(tableData));
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取点击统计数据失败", e);
            return AjaxResult.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取回调统计数据
     */
    @RequiresPermissions("ads:callback:statistics")
    @GetMapping("/callback/data")
    public AjaxResult getCallbackStatistics(
            @RequestParam(required = false) Integer advertiserId,
            @RequestParam(required = false) Integer publisherId,
            @RequestParam(required = false) String callbackType,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate beginTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endTime)
    {
        try {
            // 设置默认时间范围（最近7天）
            if (beginTime == null) {
                beginTime = LocalDate.now().minusDays(6);
            }
            if (endTime == null) {
                endTime = LocalDate.now();
            }

            Map<String, Object> result = new HashMap<>();
            
            // 获取统计概览数据
            Map<String, Object> overview = getCallbackOverview(advertiserId, publisherId, callbackType, beginTime, endTime);
            result.put("overview", overview);
            
            // 获取趋势数据
            List<Map<String, Object>> trendData = getCallbackTrendData(advertiserId, publisherId, callbackType, beginTime, endTime);
            result.put("trendData", trendData);
            
            // 获取分布数据
            Map<String, Object> distribution = getCallbackDistribution(advertiserId, publisherId, callbackType, beginTime, endTime);
            result.put("distribution", distribution);
            
            // 获取详细统计表格数据
            startPage();
            List<Map<String, Object>> tableData = getCallbackTableData(advertiserId, publisherId, callbackType, beginTime, endTime);
            result.put("tableData", getDataTable(tableData));
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取回调统计数据失败", e);
            return AjaxResult.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取点击统计概览数据
     */
    private Map<String, Object> getClickOverview(Integer advertiserId, Integer publisherId, LocalDate beginTime, LocalDate endTime) {
        Map<String, Object> overview = new HashMap<>();
        
        LocalDateTime startDateTime = beginTime.atStartOfDay();
        LocalDateTime endDateTime = endTime.atTime(LocalTime.MAX);
        
        // 总点击数
        Long totalClicks = adsClickRecordService.countByDateRange(startDateTime, endDateTime, advertiserId, publisherId);
        overview.put("totalClicks", totalClicks != null ? totalClicks : 0L);
        
        // 今日点击数
        LocalDateTime todayStart = LocalDate.now().atStartOfDay();
        LocalDateTime todayEnd = LocalDate.now().atTime(LocalTime.MAX);
        Long todayClicks = adsClickRecordService.countByDateRange(todayStart, todayEnd, advertiserId, publisherId);
        overview.put("todayClicks", todayClicks != null ? todayClicks : 0L);
        
        // 昨日点击数
        LocalDateTime yesterdayStart = LocalDate.now().minusDays(1).atStartOfDay();
        LocalDateTime yesterdayEnd = LocalDate.now().minusDays(1).atTime(LocalTime.MAX);
        Long yesterdayClicks = adsClickRecordService.countByDateRange(yesterdayStart, yesterdayEnd, advertiserId, publisherId);
        overview.put("yesterdayClicks", yesterdayClicks != null ? yesterdayClicks : 0L);
        
        // 平均单价
        Double avgPayout = adsClickRecordService.getAvgPayoutByDateRange(startDateTime, endDateTime, advertiserId, publisherId);
        overview.put("avgPayout", avgPayout != null ? avgPayout : 0.0);
        
        return overview;
    }

    /**
     * 获取回调统计概览数据
     */
    private Map<String, Object> getCallbackOverview(Integer advertiserId, Integer publisherId, String callbackType, LocalDate beginTime, LocalDate endTime) {
        Map<String, Object> overview = new HashMap<>();
        
        LocalDateTime startDateTime = beginTime.atStartOfDay();
        LocalDateTime endDateTime = endTime.atTime(LocalTime.MAX);
        
        // 总回调数
        Long totalCallbacks = adsCallbackRecordService.countByDateRange(startDateTime, endDateTime, advertiserId, publisherId, callbackType);
        overview.put("totalCallbacks", totalCallbacks != null ? totalCallbacks : 0L);
        
        // 成功回调数
        Long successCallbacks = adsCallbackRecordService.countByStatusAndDateRange(1, startDateTime, endDateTime, advertiserId, publisherId, callbackType);
        overview.put("successCallbacks", successCallbacks != null ? successCallbacks : 0L);
        
        // 失败回调数
        Long failedCallbacks = adsCallbackRecordService.countByStatusAndDateRange(2, startDateTime, endDateTime, advertiserId, publisherId, callbackType);
        overview.put("failedCallbacks", failedCallbacks != null ? failedCallbacks : 0L);
        
        // 成功率
        double successRate = totalCallbacks > 0 ? (double) successCallbacks / totalCallbacks * 100 : 0.0;
        overview.put("successRate", String.format("%.1f", successRate));
        
        return overview;
    }

    /**
     * 获取点击趋势数据
     */
    private List<Map<String, Object>> getClickTrendData(Integer advertiserId, Integer publisherId, LocalDate beginTime, LocalDate endTime) {
        List<Map<String, Object>> trendData = new ArrayList<>();
        
        LocalDate currentDate = beginTime;
        while (!currentDate.isAfter(endTime)) {
            LocalDateTime dayStart = currentDate.atStartOfDay();
            LocalDateTime dayEnd = currentDate.atTime(LocalTime.MAX);
            
            Long clickCount = adsClickRecordService.countByDateRange(dayStart, dayEnd, advertiserId, publisherId);
            
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", currentDate.format(DateTimeFormatter.ofPattern("MM-dd")));
            dayData.put("clicks", clickCount != null ? clickCount : 0L);
            
            trendData.add(dayData);
            currentDate = currentDate.plusDays(1);
        }
        
        return trendData;
    }

    /**
     * 获取回调趋势数据
     */
    private List<Map<String, Object>> getCallbackTrendData(Integer advertiserId, Integer publisherId, String callbackType, LocalDate beginTime, LocalDate endTime) {
        List<Map<String, Object>> trendData = new ArrayList<>();
        
        LocalDate currentDate = beginTime;
        while (!currentDate.isAfter(endTime)) {
            LocalDateTime dayStart = currentDate.atStartOfDay();
            LocalDateTime dayEnd = currentDate.atTime(LocalTime.MAX);
            
            Long totalCallbacks = adsCallbackRecordService.countByDateRange(dayStart, dayEnd, advertiserId, publisherId, callbackType);
            Long successCallbacks = adsCallbackRecordService.countByStatusAndDateRange(1, dayStart, dayEnd, advertiserId, publisherId, callbackType);
            
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", currentDate.format(DateTimeFormatter.ofPattern("MM-dd")));
            dayData.put("total", totalCallbacks != null ? totalCallbacks : 0L);
            dayData.put("success", successCallbacks != null ? successCallbacks : 0L);
            
            trendData.add(dayData);
            currentDate = currentDate.plusDays(1);
        }
        
        return trendData;
    }

    /**
     * 获取点击分布数据
     */
    private Map<String, Object> getClickDistribution(Integer advertiserId, Integer publisherId, LocalDate beginTime, LocalDate endTime) {
        Map<String, Object> distribution = new HashMap<>();
        
        LocalDateTime startDateTime = beginTime.atStartOfDay();
        LocalDateTime endDateTime = endTime.atTime(LocalTime.MAX);
        
        // 按广告主分布
        List<Map<String, Object>> advertiserDistribution = adsClickRecordService.getClickDistributionByAdvertiser(startDateTime, endDateTime, publisherId);
        distribution.put("advertiser", advertiserDistribution);
        
        // 按国家分布
        List<Map<String, Object>> countryDistribution = adsClickRecordService.getClickDistributionByCountry(startDateTime, endDateTime, advertiserId, publisherId);
        distribution.put("country", countryDistribution);
        
        return distribution;
    }

    /**
     * 获取回调分布数据
     */
    private Map<String, Object> getCallbackDistribution(Integer advertiserId, Integer publisherId, String callbackType, LocalDate beginTime, LocalDate endTime) {
        Map<String, Object> distribution = new HashMap<>();
        
        LocalDateTime startDateTime = beginTime.atStartOfDay();
        LocalDateTime endDateTime = endTime.atTime(LocalTime.MAX);
        
        // 按回调类型分布
        List<Map<String, Object>> typeDistribution = adsCallbackRecordService.getCallbackDistributionByType(startDateTime, endDateTime, advertiserId, publisherId);
        distribution.put("type", typeDistribution);
        
        // 按状态分布
        List<Map<String, Object>> statusDistribution = adsCallbackRecordService.getCallbackDistributionByStatus(startDateTime, endDateTime, advertiserId, publisherId, callbackType);
        distribution.put("status", statusDistribution);
        
        return distribution;
    }

    /**
     * 获取点击统计表格数据
     */
    private List<Map<String, Object>> getClickTableData(Integer advertiserId, Integer publisherId, LocalDate beginTime, LocalDate endTime) {
        LocalDateTime startDateTime = beginTime.atStartOfDay();
        LocalDateTime endDateTime = endTime.atTime(LocalTime.MAX);
        
        return adsClickRecordService.getClickStatisticsTableData(startDateTime, endDateTime, advertiserId, publisherId);
    }

    /**
     * 获取回调统计表格数据
     */
    private List<Map<String, Object>> getCallbackTableData(Integer advertiserId, Integer publisherId, String callbackType, LocalDate beginTime, LocalDate endTime) {
        LocalDateTime startDateTime = beginTime.atStartOfDay();
        LocalDateTime endDateTime = endTime.atTime(LocalTime.MAX);
        
        return adsCallbackRecordService.getCallbackStatisticsTableData(startDateTime, endDateTime, advertiserId, publisherId, callbackType);
    }
}
