package com.ruoyi.ads.controller;

import com.ruoyi.ads.domain.AdsInfo;
import com.ruoyi.ads.service.IAdsInfoService;
import com.ruoyi.ads.service.IAdsPartnerService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 广告信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/ads/info")
public class AdsInfoController extends BaseController
{
    @Autowired
    private IAdsInfoService adsInfoService;
    
    @Autowired
    private IAdsPartnerService adsPartnerService;

    /**
     * 查询广告信息列表
     */
    @RequiresPermissions("ads:info:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AdsInfo adsInfo)
    {
        startPage();
        List<AdsInfo> list = adsInfoService.selectAdsInfoList(adsInfo);
        return getDataTable(list);
    }

    /**
     * 导出广告信息列表
     */
    @RequiresPermissions("ads:info:export")
    @Log(title = "广告信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdsInfo adsInfo)
    {
        List<AdsInfo> list = adsInfoService.selectAdsInfoList(adsInfo);
        ExcelUtil<AdsInfo> util = new ExcelUtil<AdsInfo>(AdsInfo.class);
        util.exportExcel(response, list, "广告信息数据");
    }

    /**
     * 获取广告信息详细信息
     */
    @RequiresPermissions("ads:info:query")
    @GetMapping(value = "/{adsId}")
    public AjaxResult getInfo(@PathVariable("adsId") Long adsId)
    {
        return AjaxResult.success(adsInfoService.selectAdsInfoByAdsId(adsId));
    }

    /**
     * 新增广告信息
     */
    @RequiresPermissions("ads:info:add")
    @Log(title = "广告信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(AdsInfo adsInfo)
    {
        return toAjax(adsInfoService.insertAdsInfo(adsInfo));
    }

    /**
     * 修改广告信息
     */
    @RequiresPermissions("ads:info:edit")
    @Log(title = "广告信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(AdsInfo adsInfo)
    {
        return toAjax(adsInfoService.updateAdsInfo(adsInfo));
    }

    /**
     * 删除广告信息
     */
    @RequiresPermissions("ads:info:remove")
    @Log(title = "广告信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(adsInfoService.deleteAdsInfoByAdsIds(ids));
    }

    /**
     * 校验广告名称
     */
    @PostMapping("/checkAdsNameUnique")
    public String checkAdsNameUnique(AdsInfo adsInfo)
    {
        return adsInfoService.checkAdsNameUnique(adsInfo);
    }

    /**
     * 校验应用ID
     */
    @PostMapping("/checkAppIdUnique")
    public String checkAppIdUnique(AdsInfo adsInfo)
    {
        return adsInfoService.checkAppIdUnique(adsInfo);
    }

    /**
     * 获取广告主列表
     */
    @GetMapping("/advertiserList")
    public AjaxResult getAdvertiserList()
    {
        return AjaxResult.success(adsPartnerService.selectAdvertiserList());
    }

    /**
     * 根据广告主ID获取广告列表
     */
    @GetMapping("/listByAdvertiser/{advertiserId}")
    public AjaxResult getAdsByAdvertiser(@PathVariable Long advertiserId)
    {
        return AjaxResult.success(adsInfoService.selectAdsInfoByAdvertiserId(advertiserId));
    }

    /**
     * 导入广告数据
     */
    @RequiresPermissions("ads:info:import")
    @Log(title = "广告信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<AdsInfo> util = new ExcelUtil<AdsInfo>(AdsInfo.class);
        List<AdsInfo> adsInfoList = util.importExcel(file.getInputStream());
        String operName = getLoginName();
        String message = adsInfoService.importAdsInfo(adsInfoList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<AdsInfo> util = new ExcelUtil<AdsInfo>(AdsInfo.class);
        util.importTemplateExcel(response, "广告信息数据");
    }

    /**
     * 批量修改广告状态
     */
    @RequiresPermissions("ads:info:edit")
    @Log(title = "广告信息", businessType = BusinessType.UPDATE)
    @PostMapping("/changeStatus")
    public AjaxResult changeStatus(AdsInfo adsInfo)
    {
        return toAjax(adsInfoService.updateAdsInfo(adsInfo));
    }


}
