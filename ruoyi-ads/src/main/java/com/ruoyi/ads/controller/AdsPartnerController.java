package com.ruoyi.ads.controller;

import com.ruoyi.ads.domain.AdsPartner;
import com.ruoyi.ads.service.IAdsPartnerService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 合作伙伴Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/ads/partner")
public class AdsPartnerController extends BaseController
{
    @Autowired
    private IAdsPartnerService adsPartnerService;

    /**
     * 查询合作伙伴列表
     */
    @RequiresPermissions("ads:partner:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AdsPartner adsPartner)
    {
        startPage();
        List<AdsPartner> list = adsPartnerService.selectAdsPartnerList(adsPartner);
        return getDataTable(list);
    }

    /**
     * 导出合作伙伴列表
     */
    @RequiresPermissions("ads:partner:export")
    @Log(title = "合作伙伴", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdsPartner adsPartner)
    {
        List<AdsPartner> list = adsPartnerService.selectAdsPartnerList(adsPartner);
        ExcelUtil<AdsPartner> util = new ExcelUtil<AdsPartner>(AdsPartner.class);
        util.exportExcel(response, list, "合作伙伴数据");
    }

    /**
     * 获取合作伙伴详细信息
     */
    @RequiresPermissions("ads:partner:query")
    @GetMapping(value = "/{partnerId}")
    public AjaxResult getInfo(@PathVariable("partnerId") Long partnerId)
    {
        return AjaxResult.success(adsPartnerService.selectAdsPartnerByPartnerId(partnerId));
    }

    /**
     * 新增合作伙伴
     */
    @RequiresPermissions("ads:partner:add")
    @Log(title = "合作伙伴", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(AdsPartner adsPartner)
    {
        return toAjax(adsPartnerService.insertAdsPartner(adsPartner));
    }

    /**
     * 修改合作伙伴
     */
    @RequiresPermissions("ads:partner:edit")
    @Log(title = "合作伙伴", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@Validated AdsPartner adsPartner)
    {
        return toAjax(adsPartnerService.updateAdsPartner(adsPartner));
    }

    /**
     * 删除合作伙伴
     */
    @RequiresPermissions("ads:partner:remove")
    @Log(title = "合作伙伴", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(adsPartnerService.deleteAdsPartnerByPartnerIds(ids));
    }

    /**
     * 校验合作伙伴密钥
     */
    @PostMapping("/checkPartnerKeyUnique")
    public String checkPartnerKeyUnique(@RequestBody AdsPartner adsPartner)
    {
        return adsPartnerService.checkPartnerKeyUnique(adsPartner);
    }

    /**
     * 校验合作伙伴名称
     */
    @PostMapping("/checkPartnerNameUnique")
    public String checkPartnerNameUnique(AdsPartner adsPartner)
    {
        return adsPartnerService.checkPartnerNameUnique(adsPartner);
    }

    /**
     * 生成合作伙伴密钥
     */
    @GetMapping("/generatePartnerKey")
    public AjaxResult generatePartnerKey()
    {
        return AjaxResult.success("success",adsPartnerService.generatePartnerKey());
    }

    /**
     * 获取广告主列表
     */
    @GetMapping("/advertiserList")
    public AjaxResult getAdvertiserList()
    {
        return AjaxResult.success(adsPartnerService.selectAdvertiserList());
    }

    /**
     * 获取开发者列表
     */
    @GetMapping("/publisherList")
    public AjaxResult getPublisherList()
    {
        return AjaxResult.success(adsPartnerService.selectPublisherList());
    }

    /**
     * 根据合作伙伴类型查询列表
     */
    @GetMapping("/listByType/{partnerType}")
    public AjaxResult getPartnersByType(@PathVariable Integer partnerType)
    {
        return AjaxResult.success(adsPartnerService.selectAdsPartnerByPartnerType(partnerType));
    }

    /**
     * 导入合作伙伴数据
     */
    @RequiresPermissions("ads:partner:import")
    @Log(title = "合作伙伴", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<AdsPartner> util = new ExcelUtil<AdsPartner>(AdsPartner.class);
        List<AdsPartner> adsPartnerList = util.importExcel(file.getInputStream());
        String operName = getLoginName();
        String message = adsPartnerService.importAdsPartner(adsPartnerList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<AdsPartner> util = new ExcelUtil<AdsPartner>(AdsPartner.class);
        util.importTemplateExcel(response, "合作伙伴数据");
    }

    /**
     * 批量修改合作伙伴状态
     */
    @RequiresPermissions("ads:partner:edit")
    @Log(title = "合作伙伴", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody AdsPartner adsPartner)
    {
        return toAjax(adsPartnerService.updateAdsPartner(adsPartner));
    }
}
