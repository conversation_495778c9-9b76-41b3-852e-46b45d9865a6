package com.ruoyi.ads.controller;

import com.ruoyi.ads.domain.AdsPartner;
import com.ruoyi.ads.domain.AdsPublisherOffer;
import com.ruoyi.ads.service.IAdsInfoService;
import com.ruoyi.ads.service.IAdsPartnerService;
import com.ruoyi.ads.service.IAdsPublisherOfferService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 广告配置Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/ads/offer")
public class AdsPublisherOfferController extends BaseController
{
    @Autowired
    private IAdsPublisherOfferService adsPublisherOfferService;
    
    @Autowired
    private IAdsPartnerService adsPartnerService;
    
    @Autowired
    private IAdsInfoService adsInfoService;

    /**
     * 查询广告配置列表
     */
    @RequiresPermissions("ads:offer:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AdsPublisherOffer adsPublisherOffer)
    {
        startPage();
        List<AdsPublisherOffer> list = adsPublisherOfferService.selectAdsPublisherOfferListWithRelated(adsPublisherOffer);
        return getDataTable(list);
    }

    /**
     * 导出广告配置列表
     */
    @RequiresPermissions("ads:offer:export")
    @Log(title = "广告配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdsPublisherOffer adsPublisherOffer)
    {
        List<AdsPublisherOffer> list = adsPublisherOfferService.selectAdsPublisherOfferListWithRelated(adsPublisherOffer);
        ExcelUtil<AdsPublisherOffer> util = new ExcelUtil<AdsPublisherOffer>(AdsPublisherOffer.class);
        util.exportExcel(response, list, "广告配置数据");
    }

    /**
     * 获取广告配置详细信息
     */
    @RequiresPermissions("ads:offer:query")
    @GetMapping(value = "/{offerId}")
    public AjaxResult getInfo(@PathVariable("offerId") Long offerId)
    {
        return AjaxResult.success(adsPublisherOfferService.selectAdsPublisherOfferByOfferId(offerId));
    }

    /**
     * 新增广告配置
     */
    @RequiresPermissions("ads:offer:add")
    @Log(title = "广告配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(AdsPublisherOffer adsPublisherOffer)
    {
        return toAjax(adsPublisherOfferService.insertAdsPublisherOffer(adsPublisherOffer));
    }

    /**
     * 修改广告配置
     */
    @RequiresPermissions("ads:offer:edit")
    @Log(title = "广告配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(AdsPublisherOffer adsPublisherOffer)
    {
        return toAjax(adsPublisherOfferService.updateAdsPublisherOffer(adsPublisherOffer));
    }

    /**
     * 删除广告配置
     */
    @RequiresPermissions("ads:offer:remove")
    @Log(title = "广告配置", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(adsPublisherOfferService.deleteAdsPublisherOfferByOfferIds(ids));
    }

    /**
     * 校验广告和开发者组合是否唯一
     */
    @PostMapping("/checkAdsPublisherUnique")
    public String checkAdsPublisherUnique(AdsPublisherOffer adsPublisherOffer)
    {
        return adsPublisherOfferService.checkAdsPublisherUnique(adsPublisherOffer);
    }

    /**
     * 获取广告主列表
     */
    @GetMapping("/advertiserList")
    public AjaxResult getAdvertiserList()
    {
        return AjaxResult.success(adsPartnerService.selectAdvertiserList());
    }

    /**
     * 获取开发者列表
     */
    @GetMapping("/publisherList")
    public AjaxResult getPublisherList()
    {
        return AjaxResult.success(adsPartnerService.selectPublisherList());
    }

    /**
     * 根据广告主ID获取广告列表
     */
    @GetMapping("/adsByAdvertiser/{advertiserId}")
    public AjaxResult getAdsByAdvertiser(@PathVariable Long advertiserId)
    {
        return AjaxResult.success(adsInfoService.selectAdsInfoByAdvertiserId(advertiserId));
    }

    /**
     * 根据开发者ID查询配置列表
     */
    @GetMapping("/listByPublisher/{publisherId}")
    public AjaxResult getOffersByPublisher(@PathVariable Long publisherId)
    {
        return AjaxResult.success(adsPublisherOfferService.selectAdsPublisherOfferByPublisherId(publisherId));
    }

    /**
     * 根据广告ID查询配置列表
     */
    @GetMapping("/listByAds/{adsId}")
    public AjaxResult getOffersByAds(@PathVariable Long adsId)
    {
        return AjaxResult.success(adsPublisherOfferService.selectAdsPublisherOfferByAdsId(adsId));
    }

    /**
     * 生成点击链接
     */
    @PostMapping("/generateClickUrl")
    public AjaxResult generateClickUrl(AdsPublisherOffer adsPublisherOffer)
    {
        // 获取开发者信息
        AdsPartner publisher = adsPartnerService.selectAdsPartnerByPartnerId(adsPublisherOffer.getPublisherId());
        if (publisher == null)
        {
            return AjaxResult.error("开发者不存在");
        }

        // 如果是编辑模式且有offerId，使用真实的offerId；否则使用占位符
        String clickUrl = adsPublisherOfferService.generateClickUrl(publisher.getPartnerKey(), adsPublisherOffer.getOfferId());
        return AjaxResult.success("success",clickUrl);
    }

    /**
     * 批量创建广告配置
     */
    @RequiresPermissions("ads:offer:add")
    @Log(title = "广告配置", businessType = BusinessType.INSERT)
    @PostMapping("/batchCreate")
    public AjaxResult batchCreate(@RequestBody AdsPublisherOffer adsPublisherOffer)
    {
        // 这里需要前端传递广告ID数组
        // 为了简化，这里先返回错误，实际使用时需要修改前端传参
        return AjaxResult.error("批量创建功能需要传递广告ID数组");
    }

    /**
     * 批量修改广告配置状态
     */
    @RequiresPermissions("ads:offer:edit")
    @Log(title = "广告配置", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody AdsPublisherOffer adsPublisherOffer)
    {
        return toAjax(adsPublisherOfferService.updateAdsPublisherOffer(adsPublisherOffer));
    }
}
