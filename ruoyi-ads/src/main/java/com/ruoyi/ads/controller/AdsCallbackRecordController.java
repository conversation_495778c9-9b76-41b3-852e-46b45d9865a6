package com.ruoyi.ads.controller;

import com.ruoyi.ads.domain.AdsCallbackRecord;
import com.ruoyi.ads.service.IAdsCallbackRecordService;
import com.ruoyi.ads.service.IAdsPartnerService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 回调记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@RestController
@RequestMapping("/ads/callback/record")
public class AdsCallbackRecordController extends BaseController
{
    @Autowired
    private IAdsCallbackRecordService adsCallbackRecordService;
    
    @Autowired
    private IAdsPartnerService adsPartnerService;

    /**
     * 查询回调记录列表
     */
    @RequiresPermissions("ads:callback:records")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AdsCallbackRecord adsCallbackRecord)
    {
        startPage();
        List<AdsCallbackRecord> list = adsCallbackRecordService.selectAdsCallbackRecordListWithRelated(adsCallbackRecord);
        return getDataTable(list);
    }

    /**
     * 导出回调记录列表
     */
    @RequiresPermissions("ads:callback:records")
    @Log(title = "回调记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdsCallbackRecord adsCallbackRecord)
    {
        List<AdsCallbackRecord> list = adsCallbackRecordService.selectAdsCallbackRecordListWithRelated(adsCallbackRecord);
        ExcelUtil<AdsCallbackRecord> util = new ExcelUtil<AdsCallbackRecord>(AdsCallbackRecord.class);
        util.exportExcel(response, list, "回调记录数据");
    }

    /**
     * 获取回调记录详细信息
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return AjaxResult.success(adsCallbackRecordService.selectAdsCallbackRecordByRecordId(recordId));
    }

    /**
     * 新增回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @Log(title = "回调记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdsCallbackRecord adsCallbackRecord)
    {
        return toAjax(adsCallbackRecordService.insertAdsCallbackRecord(adsCallbackRecord));
    }

    /**
     * 修改回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @Log(title = "回调记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdsCallbackRecord adsCallbackRecord)
    {
        return toAjax(adsCallbackRecordService.updateAdsCallbackRecord(adsCallbackRecord));
    }

    /**
     * 获取回调记录详细信息
     */
    //@RequiresPermissions("ads:callback:records")
    //@GetMapping(value = "/{recordId}")
    //public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    //{
    //    return AjaxResult.success(adsCallbackRecordService.selectAdsCallbackRecordByRecordId(recordId));
    //}

    /**
     * 删除回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @Log(title = "回调记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(adsCallbackRecordService.deleteAdsCallbackRecordByRecordIds(recordIds));
    }

    /**
     * 根据点击ID查询回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/byClickId/{clickId}")
    public AjaxResult getByClickId(@PathVariable String clickId)
    {
        return AjaxResult.success(adsCallbackRecordService.selectAdsCallbackRecordByClickId(clickId));
    }

    /**
     * 根据回调类型查询回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/byType/{callbackType}")
    public AjaxResult getByType(@PathVariable String callbackType)
    {
        return AjaxResult.success(adsCallbackRecordService.selectAdsCallbackRecordByCallbackType(callbackType));
    }

    /**
     * 根据回调状态查询回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/byStatus/{callbackStatus}")
    public AjaxResult getByStatus(@PathVariable Integer callbackStatus)
    {
        return AjaxResult.success(adsCallbackRecordService.selectAdsCallbackRecordByCallbackStatus(callbackStatus));
    }

    /**
     * 根据开发者ID查询回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/byPublisher/{publisherId}")
    public AjaxResult getByPublisher(@PathVariable Long publisherId)
    {
        return AjaxResult.success(adsCallbackRecordService.selectAdsCallbackRecordByPublisherId(publisherId));
    }

    /**
     * 根据广告主ID查询回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/byAdvertiser/{advertiserId}")
    public AjaxResult getByAdvertiser(@PathVariable Long advertiserId)
    {
        return AjaxResult.success(adsCallbackRecordService.selectAdsCallbackRecordByAdvertiserId(advertiserId));
    }

    /**
     * 根据广告ID查询回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/byAds/{adsId}")
    public AjaxResult getByAds(@PathVariable Long adsId)
    {
        return AjaxResult.success(adsCallbackRecordService.selectAdsCallbackRecordByAdsId(adsId));
    }

    /**
     * 获取回调记录统计
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Long totalCallbacks = adsCallbackRecordService.countAdsCallbackRecord();
        Long successCallbacks = adsCallbackRecordService.countAdsCallbackRecordByCallbackStatus(1);
        Long failedCallbacks = adsCallbackRecordService.countAdsCallbackRecordByCallbackStatus(2);
        Long pendingCallbacks = adsCallbackRecordService.countAdsCallbackRecordByCallbackStatus(0);
        
        return AjaxResult.success()
                .put("totalCallbacks", totalCallbacks)
                .put("successCallbacks", successCallbacks)
                .put("failedCallbacks", failedCallbacks)
                .put("pendingCallbacks", pendingCallbacks);
    }

    /**
     * 获取待重试的回调记录
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/retryable")
    public AjaxResult getRetryableRecords()
    {
        return AjaxResult.success(adsCallbackRecordService.selectRetryableCallbackRecords());
    }

    /**
     * 重试失败的回调
     */
    @RequiresPermissions("ads:callback:records")
    @Log(title = "回调记录", businessType = BusinessType.UPDATE)
    @PostMapping("/retry/{recordId}")
    public AjaxResult retryCallback(@PathVariable Long recordId)
    {
        return toAjax(adsCallbackRecordService.retryCallback(recordId));
    }

    /**
     * 获取广告主列表
     */
    @GetMapping("/advertiserList")
    public AjaxResult getAdvertiserList()
    {
        return AjaxResult.success(adsPartnerService.selectAdvertiserList());
    }

    /**
     * 获取开发者列表
     */
    @GetMapping("/publisherList")
    public AjaxResult getPublisherList()
    {
        return AjaxResult.success(adsPartnerService.selectPublisherList());
    }

    /**
     * 清理过期记录
     */
    @RequiresPermissions("ads:callback:records")
    @Log(title = "回调记录", businessType = BusinessType.CLEAN)
    @PostMapping("/clean/{days}")
    public AjaxResult cleanExpiredRecords(@PathVariable int days)
    {
        int cleanedCount = adsCallbackRecordService.cleanExpiredRecords(days);
        return AjaxResult.success("成功清理 " + cleanedCount + " 条过期记录");
    }
}
