package com.ruoyi.ads.controller;

import com.ruoyi.ads.service.IAdsCacheService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 缓存管理控制器
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@RestController
@RequestMapping("/ads/cache")
public class AdsCacheController extends BaseController
{
    @Autowired
    private IAdsCacheService adsCacheService;

    
    /**
     * 获取缓存统计信息
     */
    @RequiresPermissions("ads:cache:view")
    @GetMapping("/stats")
    public AjaxResult getCacheStats()
    {
        String stats = adsCacheService.getCacheStats();
        return AjaxResult.success("获取缓存统计信息成功", stats);
    }
    
    /**
     * 清除所有缓存
     */
    @RequiresPermissions("ads:cache:clear")
    @Log(title = "缓存管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clear")
    public AjaxResult clearAllCache()
    {
        adsCacheService.evictAllCache();
        return AjaxResult.success("清除所有缓存成功");
    }
    
    /**
     * 清除合作伙伴缓存
     */
    @RequiresPermissions("ads:cache:clear")
    @Log(title = "缓存管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/partner/{partnerId}")
    public AjaxResult clearPartnerCache(@PathVariable Long partnerId)
    {
        adsCacheService.evictPartnerCache(partnerId);
        return AjaxResult.success("清除合作伙伴缓存成功");
    }
    
    /**
     * 清除合作伙伴Key缓存
     */
    @RequiresPermissions("ads:cache:clear")
    @Log(title = "缓存管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/partner/key/{partnerKey}")
    public AjaxResult clearPartnerKeyCache(@PathVariable String partnerKey)
    {
        adsCacheService.evictPartnerKeyCache(partnerKey);
        return AjaxResult.success("清除合作伙伴Key缓存成功");
    }
    
    /**
     * 清除广告信息缓存
     */
    @RequiresPermissions("ads:cache:clear")
    @Log(title = "缓存管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/ads/{adsId}")
    public AjaxResult clearAdsInfoCache(@PathVariable Long adsId)
    {
        adsCacheService.evictAdsInfoCache(adsId);
        return AjaxResult.success("清除广告信息缓存成功");
    }
    
    /**
     * 清除发布者配置缓存
     */
    @RequiresPermissions("ads:cache:clear")
    @Log(title = "缓存管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/publisher-offer/{offerId}")
    public AjaxResult clearPublisherOfferCache(@PathVariable Long offerId)
    {
        adsCacheService.evictPublisherOfferCache(offerId);
        return AjaxResult.success("清除发布者配置缓存成功");
    }
    
    /**
     * 清除广告主配置缓存
     */
    @RequiresPermissions("ads:cache:clear")
    @Log(title = "缓存管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/advertiser-offer/{offerId}")
    public AjaxResult clearAdvertiserOfferCache(@PathVariable Long offerId)
    {
        adsCacheService.evictAdvertiserOfferCache(offerId);
        return AjaxResult.success("清除广告主配置缓存成功");
    }
}
