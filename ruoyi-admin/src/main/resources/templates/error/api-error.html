<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API服务错误</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 40px;
            max-width: 500px;
            text-align: center;
            margin: 20px;
        }
        
        .error-icon {
            font-size: 64px;
            color: #ff6b6b;
            margin-bottom: 20px;
        }
        
        .error-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .error-message {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .error-details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #495057;
            border-left: 4px solid #ff6b6b;
        }
        
        .error-code {
            font-weight: bold;
            color: #dc3545;
        }
        
        .help-text {
            font-size: 14px;
            color: #6c757d;
            margin-top: 20px;
        }
        
        .contact-info {
            background: #e3f2fd;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #1976d2;
        }
        
        .timestamp {
            font-size: 12px;
            color: #adb5bd;
            margin-top: 15px;
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            text-align: left;
            font-size: 12px;
            color: #856404;
        }
        
        @media (max-width: 600px) {
            .error-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .error-icon {
                font-size: 48px;
            }
            
            .error-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">🚫</div>
        <h1 class="error-title">API服务错误</h1>
        <p class="error-message">
            很抱歉，API服务暂时无法处理您的请求。请稍后重试或联系技术支持。
        </p>
        
        <div class="error-details">
            <div><span class="error-code">错误代码:</span> <span th:text="${errorCode}">500</span></div>
            <div><span class="error-code">错误信息:</span> <span th:text="${errorMessage}">服务器内部错误</span></div>
            <div th:if="${requestId}"><span class="error-code">请求ID:</span> <span th:text="${requestId}">N/A</span></div>
        </div>
        
        <div class="help-text">
            <strong>可能的原因：</strong><br>
            • 服务器正在维护中<br>
            • 网络连接异常<br>
            • 请求参数不正确<br>
            • 系统负载过高
        </div>
        
        <div class="contact-info">
            <strong>需要帮助？</strong><br>
            请联系技术支持并提供上述错误信息，我们将尽快为您解决问题。
        </div>
        
        <div th:if="${debugMode}" class="debug-info">
            <strong>调试信息（仅开发环境显示）:</strong><br>
            <div>环境: 开发/测试</div>
            <div>时间戳: <span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss.SSS')}"></span></div>
        </div>
        
        <div class="timestamp" th:text="'错误时间: ' + ${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss')}">
            错误时间: 2025-01-24 12:28:12
        </div>
    </div>
</body>
</html>
