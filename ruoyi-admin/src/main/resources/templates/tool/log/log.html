<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('系统日志')" />
    <th:block th:include="include :: datetimepicker-css" />
    <style>
        .log-container {
            height: calc(100vh - 200px);
            min-height: 400px;
        }
        .log-content {
            background-color: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            padding: 10px;
            border-radius: 4px;
            overflow-y: auto;
            height: 100%;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .log-line {
            margin-bottom: 2px;
            padding: 2px 5px;
            border-radius: 2px;
        }
        .log-line:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        .log-line-number {
            color: #858585;
            margin-right: 10px;
            min-width: 50px;
            display: inline-block;
            text-align: right;
        }
        .log-level-ERROR {
            color: #f44336;
            font-weight: bold;
        }
        .log-level-WARN {
            color: #ff9800;
            font-weight: bold;
        }
        .log-level-INFO {
            color: #4caf50;
        }
        .log-level-DEBUG {
            color: #2196f3;
        }
        .log-timestamp {
            color: #9c9c9c;
        }
        .fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background: white;
            padding: 20px;
        }
        .fullscreen .log-container {
            height: calc(100vh - 120px);
        }
        .stats-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .stats-item {
            display: inline-block;
            margin-right: 20px;
            text-align: center;
        }
        .stats-number {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }
        .stats-label {
            font-size: 12px;
            color: #6c757d;
        }
        .search-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .auto-refresh {
            color: #28a745;
        }
        .auto-refresh.disabled {
            color: #6c757d;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div" id="main-container">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <div class="search-form">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <label class="control-label">日志文件：</label>
                                <select id="logFileSelect" class="form-control">
                                    <option value="">请选择日志文件</option>
                                    <option th:each="file : ${logFiles}" 
                                            th:value="${file.fileName}" 
                                            th:text="${file.fileName} + ' (' + ${file.fileSize} + ')'">
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="control-label">开始日期：</label>
                                <div class="input-group date">
                                    <input id="startDate" class="form-control" placeholder="yyyy-mm-dd" name="startDate" />
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="control-label">结束日期：</label>
                                <div class="input-group date">
                                    <input id="endDate" class="form-control" placeholder="yyyy-mm-dd" name="endDate" />
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="control-label">日志级别：</label>
                                <select id="logLevel" class="form-control">
                                    <option value="">全部</option>
                                    <option value="ERROR">ERROR</option>
                                    <option value="WARN">WARN</option>
                                    <option value="INFO">INFO</option>
                                    <option value="DEBUG">DEBUG</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <div class="form-group">
                                <label class="control-label">关键字：</label>
                                <input id="keyword" class="form-control" type="text" placeholder="搜索关键字" />
                            </div>
                        </div>
                        <div class="col-sm-1">
                            <div class="form-group" style="margin-top: 25px;">
                                <button type="button" class="btn btn-primary" onclick="searchLog()">
                                    <i class="fa fa-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <div class="row">
                    <div class="col-sm-12">
                        <!-- 统计信息 -->
                        <div class="stats-card" id="statsCard" style="display: none;">
                            <div class="stats-item">
                                <div class="stats-number" id="fileSizeStats">-</div>
                                <div class="stats-label">文件大小</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-number" id="lineCountStats">-</div>
                                <div class="stats-label">总行数</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-number text-danger" id="errorCountStats">-</div>
                                <div class="stats-label">ERROR</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-number text-warning" id="warnCountStats">-</div>
                                <div class="stats-label">WARN</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-number text-success" id="infoCountStats">-</div>
                                <div class="stats-label">INFO</div>
                            </div>
                            <div class="stats-item">
                                <div class="stats-number text-info" id="debugCountStats">-</div>
                                <div class="stats-label">DEBUG</div>
                            </div>
                        </div>

                        <!-- 工具栏 -->
                        <div class="btn-group-sm" id="toolbar" role="group">
                            <button type="button" class="btn btn-success" onclick="refreshLog()">
                                <i class="fa fa-refresh"></i> 刷新
                            </button>
                            <button type="button" class="btn btn-info" onclick="toggleAutoRefresh()">
                                <i class="fa fa-clock-o"></i> <span id="autoRefreshText">自动刷新</span>
                            </button>
                            <button type="button" class="btn btn-warning" onclick="toggleFullscreen()">
                                <i class="fa fa-expand"></i> <span id="fullscreenText">全屏</span>
                            </button>
                            <button type="button" class="btn btn-primary" onclick="downloadLog()" shiro:hasPermission="tool:log:download">
                                <i class="fa fa-download"></i> 下载
                            </button>
                            <button type="button" class="btn btn-danger" onclick="cleanLogs()" shiro:hasPermission="tool:log:clean">
                                <i class="fa fa-trash"></i> 清理
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 日志内容显示区域 -->
                <div class="log-container" id="logContainer">
                    <div class="log-content" id="logContent">
                        <div class="text-center" style="color: #6c757d; margin-top: 50px;">
                            <i class="fa fa-file-text-o fa-3x"></i>
                            <p>请选择日志文件查看内容</p>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="row" id="paginationRow" style="display: none;">
                    <div class="col-sm-12">
                        <div id="pagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "tool/log";
        var currentFileName = "";
        var autoRefreshInterval = null;
        var isFullscreen = false;
        var currentPage = 1;
        var pageSize = 100;

        $(function() {
            // 初始化日期选择器
            $('#startDate').datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                language: 'zh-CN',
                autoclose: true,
                todayBtn: true
            });
            
            $('#endDate').datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                language: 'zh-CN',
                autoclose: true,
                todayBtn: true
            });

            // 文件选择事件
            $('#logFileSelect').change(function() {
                var fileName = $(this).val();
                if (fileName) {
                    currentFileName = fileName;
                    loadLogStats();
                    searchLog();
                } else {
                    currentFileName = "";
                    $('#logContent').html('<div class="text-center" style="color: #6c757d; margin-top: 50px;"><i class="fa fa-file-text-o fa-3x"></i><p>请选择日志文件查看内容</p></div>');
                    $('#statsCard').hide();
                    $('#paginationRow').hide();
                }
            });

            // 回车搜索
            $('#keyword').keypress(function(e) {
                if (e.which == 13) {
                    searchLog();
                }
            });
        });

        // 搜索日志
        function searchLog() {
            if (!currentFileName) {
                $.modal.alertWarning("请先选择日志文件");
                return;
            }

            var data = {
                fileName: currentFileName,
                startDate: $('#startDate').val(),
                endDate: $('#endDate').val(),
                logLevel: $('#logLevel').val(),
                keyword: $('#keyword').val(),
                pageNum: currentPage,
                pageSize: pageSize
            };

            $.ajax({
                url: prefix + "/content",
                type: "post",
                data: data,
                success: function(result) {
                    if (result.code == 0) {
                        displayLogContent(result.rows);
                        updatePagination(result.total);
                        $('#paginationRow').show();
                    } else {
                        $.modal.alertError("加载日志失败: " + result.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("加载日志失败");
                }
            });
        }

        // 显示日志内容
        function displayLogContent(logLines) {
            var html = '';
            if (logLines && logLines.length > 0) {
                for (var i = 0; i < logLines.length; i++) {
                    var line = logLines[i];
                    html += '<div class="log-line">';
                    html += '<span class="log-line-number">' + line.lineNumber + '</span>';
                    html += '<span class="log-level-' + line.level + '">' + escapeHtml(line.content) + '</span>';
                    html += '</div>';
                }
            } else {
                html = '<div class="text-center" style="color: #6c757d; margin-top: 50px;"><i class="fa fa-search fa-3x"></i><p>没有找到匹配的日志记录</p></div>';
            }
            $('#logContent').html(html);
        }

        // 加载日志统计信息
        function loadLogStats() {
            if (!currentFileName) return;

            $.ajax({
                url: prefix + "/stats",
                type: "post",
                data: { fileName: currentFileName },
                success: function(result) {
                    if (result.code == 200) {
                        var data = result.data;
                        $('#fileSizeStats').text(data.fileSize);
                        $('#lineCountStats').text(data.lineCount);
                        $('#errorCountStats').text(data.errorCount);
                        $('#warnCountStats').text(data.warnCount);
                        $('#infoCountStats').text(data.infoCount);
                        $('#debugCountStats').text(data.debugCount);
                        $('#statsCard').show();
                    }
                }
            });
        }

        // 更新分页
        function updatePagination(total) {
            var totalPages = Math.ceil(total / pageSize);
            var paginationHtml = '';
            
            if (totalPages > 1) {
                paginationHtml += '<nav><ul class="pagination">';
                
                // 上一页
                if (currentPage > 1) {
                    paginationHtml += '<li><a href="javascript:void(0)" onclick="goToPage(' + (currentPage - 1) + ')">上一页</a></li>';
                }
                
                // 页码
                var startPage = Math.max(1, currentPage - 2);
                var endPage = Math.min(totalPages, currentPage + 2);
                
                for (var i = startPage; i <= endPage; i++) {
                    if (i == currentPage) {
                        paginationHtml += '<li class="active"><a href="javascript:void(0)">' + i + '</a></li>';
                    } else {
                        paginationHtml += '<li><a href="javascript:void(0)" onclick="goToPage(' + i + ')">' + i + '</a></li>';
                    }
                }
                
                // 下一页
                if (currentPage < totalPages) {
                    paginationHtml += '<li><a href="javascript:void(0)" onclick="goToPage(' + (currentPage + 1) + ')">下一页</a></li>';
                }
                
                paginationHtml += '</ul></nav>';
                paginationHtml += '<div class="text-right">共 ' + total + ' 条记录，第 ' + currentPage + '/' + totalPages + ' 页</div>';
            }
            
            $('#pagination').html(paginationHtml);
        }

        // 跳转页面
        function goToPage(page) {
            currentPage = page;
            searchLog();
        }

        // 刷新日志
        function refreshLog() {
            if (currentFileName) {
                loadLogStats();
                searchLog();
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                $('#autoRefreshText').text('自动刷新').removeClass('auto-refresh').addClass('auto-refresh disabled');
            } else {
                autoRefreshInterval = setInterval(refreshLog, 5000); // 5秒刷新一次
                $('#autoRefreshText').text('停止刷新').removeClass('auto-refresh disabled').addClass('auto-refresh');
            }
        }

        // 切换全屏
        function toggleFullscreen() {
            var container = $('#main-container');
            if (isFullscreen) {
                container.removeClass('fullscreen');
                $('#fullscreenText').text('全屏');
                $('body').removeClass('gray-bg');
                isFullscreen = false;
            } else {
                container.addClass('fullscreen');
                $('#fullscreenText').text('退出全屏');
                $('body').addClass('gray-bg');
                isFullscreen = true;
            }
        }

        // 下载日志
        function downloadLog() {
            if (!currentFileName) {
                $.modal.alertWarning("请先选择日志文件");
                return;
            }
            window.open(prefix + "/download?fileName=" + encodeURIComponent(currentFileName));
        }

        // 清理日志
        function cleanLogs() {
            $.modal.confirm("确定要清理30天前的日志文件吗？", function() {
                $.ajax({
                    url: prefix + "/clean",
                    type: "post",
                    data: { days: 30 },
                    success: function(result) {
                        if (result.code == 200) {
                            $.modal.alertSuccess("清理日志文件成功");
                            // 刷新文件列表
                            location.reload();
                        } else {
                            $.modal.alertError("清理日志文件失败: " + result.msg);
                        }
                    }
                });
            });
        }

        // HTML转义
        function escapeHtml(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }

        // 页面卸载时清理定时器
        $(window).on('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
