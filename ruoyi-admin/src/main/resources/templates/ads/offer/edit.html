<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改广告配置')" />
    <th:block th:include="include :: select2-css" />
    <style>
        .compact-form .form-group {
            margin-bottom: 15px;
        }
        .compact-form .form-group.compact {
            margin-bottom: 10px;
        }
        .compact-form .row {
            margin-bottom: 10px;
        }
        .compact-form .col-sm-6 {
            padding-left: 8px;
            padding-right: 8px;
        }
        .compact-form .control-label {
            font-weight: 600;
            color: #495057;
        }
        .number-input {
            width: 120px;
            display: inline-block;
        }
        .panel {
            margin-bottom: 20px;
        }
        .panel-heading {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .panel-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal compact-form" id="form-offer-edit" th:object="${adsPublisherOffer}">
            <input name="offerId" th:field="*{offerId}" type="hidden">

            <!-- 基本信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info-circle"></i> 基本信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">广告主：</label>
                                <div class="col-sm-8">
                                    <select id="advertiserId" name="advertiserId" class="form-control" required th:field="*{advertiserId}">
                                        <option value="">请选择广告主</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">开发者：</label>
                                <div class="col-sm-8">
                                    <select id="publisherId" name="publisherId" class="form-control" required th:field="*{publisherId}">
                                        <option value="">请选择开发者</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">广告：</label>
                                <div class="col-sm-8">
                                    <select id="adsId" name="adsId" class="form-control" required th:field="*{adsId}">
                                        <option value="">请先选择广告主</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">投放国家：</label>
                                <div class="col-sm-8">
                                    <input name="country" th:field="*{country}" class="form-control" type="text" placeholder="多个国家用逗号分隔，如：US,UK,CA">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">单价($)：</label>
                                <div class="col-sm-8">
                                    <input name="payout" th:field="*{payout}" class="form-control number-input" type="number"  min="0" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 价格和限制 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-dollar"></i> 价格和限制</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">安装上限：</label>
                                <div class="col-sm-8">
                                    <input name="installCap" th:field="*{installCap}" class="form-control number-input" type="number" min="0" placeholder="0表示无限制" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">日上限：</label>
                                <div class="col-sm-8">
                                    <input name="dailyCap" th:field="*{dailyCap}" class="form-control number-input" type="number" min="0" placeholder="0表示无限制" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">优先级：</label>
                                <div class="col-sm-8">
                                    <input name="priority" th:field="*{priority}" class="form-control number-input" type="number" min="1">
                                    <span class="help-block m-b-none">数字越小优先级越高</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">KPI：</label>
                                <div class="col-sm-8">
                                    <input name="kpi" th:field="*{kpi}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 链接信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-link"></i> 链接信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group compact">
                                <label class="col-sm-2 control-label">点击链接：</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <input name="clickUrl" th:field="*{clickUrl}" class="form-control" type="text" >
                                        <span class="input-group-btn">
                                            <button class="btn btn-default" type="button" onclick="generateClickUrl()">
                                                <i class="fa fa-refresh"></i> 重新生成
                                            </button>
                                        </span>
                                    </div>
                                    <span class="help-block m-b-none">
                                        <i class="fa fa-warning text-warning"></i>
                                        重新生成链接会影响现有的追踪，请谨慎操作！
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group compact">
                                <label class="col-sm-2 control-label">预览链接：</label>
                                <div class="col-sm-10">
                                    <input name="previewUrl" th:field="*{previewUrl}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-cog"></i> 其他信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">状态：</label>
                                <div class="col-sm-8">
                                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                                        <input type="radio" th:id="${'edit_status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
                                        <label th:for="${'edit_status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group compact">
                                <label class="col-sm-2 control-label">备注：</label>
                                <div class="col-sm-10">
                                    <textarea name="remark" th:field="*{remark}" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "ads/offer";
        $("#form-offer-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-offer-edit').serialize());
            }
        }

        $(function() {
            // 加载广告主列表
            $.get(prefix + "/advertiserList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#advertiserId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                    // 设置当前选中值
                    $select.val([[${adsPublisherOffer.advertiserId}]]);
                    // 触发change事件加载广告列表
                    $select.trigger('change');
                }
            });

            // 加载开发者列表
            $.get(prefix + "/publisherList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#publisherId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                    // 设置当前选中值
                    $select.val([[${adsPublisherOffer.publisherId}]]);
                }
            });

            // 广告主选择变化时加载对应的广告列表
            $("#advertiserId").change(function() {
                var advertiserId = $(this).val();
                var $adsSelect = $("#adsId");
                var currentAdsId = [[${adsPublisherOffer.adsId}]];
                $adsSelect.empty().append('<option value="">请选择广告</option>');
                
                if (advertiserId) {
                    $.get(prefix + "/adsByAdvertiser/" + advertiserId, function(result) {
                        if (result.code == web_status.SUCCESS) {
                            var data = result.data;
                            for (var i = 0; i < data.length; i++) {
                                $adsSelect.append('<option value="' + data[i].adsId + '">' + data[i].adsName + '</option>');
                            }
                            // 设置当前选中值
                            if (currentAdsId) {
                                $adsSelect.val(currentAdsId);
                            }
                        }
                    });
                }
            });

            // 广告和开发者组合唯一性验证
            $("#adsId, #publisherId").blur(function() {
                var adsId = $("#adsId").val();
                var publisherId = $("#publisherId").val();
                var offerId = $("input[name='offerId']").val();
                if (adsId && publisherId) {
                    $.post(prefix + "/checkAdsPublisherUnique", { "adsId": adsId, "publisherId": publisherId, "offerId": offerId }, function(result) {
                        if (result == "1") {
                            $.modal.alertWarning("该广告已配置给此开发者！");
                            $("#adsId").focus();
                        }
                    });
                }
            });
        });

        // 生成点击链接
        function generateClickUrl() {
            $.modal.confirm("重新生成链接会影响现有的追踪，确定要继续吗？", function() {
                var publisherId = $("#publisherId").val();
                var offerId = $("input[name='offerId']").val();

                if (publisherId && offerId) {
                    var data = {
                        publisherId: publisherId,
                        offerId: offerId
                    };

                    $.post(prefix + "/generateClickUrl", data, function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $("input[name='clickUrl']").val(result.data);
                            $.modal.alertSuccess("点击链接已重新生成！");
                        }
                    });
                }
            });
        }
    </script>
</body>
</html>
