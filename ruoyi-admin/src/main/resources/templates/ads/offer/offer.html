<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('广告配置列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>广告主：</label>
                                <select name="advertiserId" id="searchAdvertiserId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>开发者：</label>
                                <select name="publisherId" id="searchPublisherId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>广告ID：</label>
                                <input type="text" name="adsId"/>
                            </li>
                            <li>
                                <label>投放国家：</label>
                                <input type="text" name="country"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ads:offer:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-info single disabled" onclick="$.operate.detail()" shiro:hasPermission="ads:offer:query">
                    <i class="fa fa-search-plus"></i> 查看
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ads:offer:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="ads:offer:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ads:offer:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="batchCreate()" shiro:hasPermission="ads:offer:add">
                    <i class="fa fa-plus-square"></i> 批量创建
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('ads:offer:edit')}]];
        var removeFlag = [[${@permission.hasPermi('ads:offer:remove')}]];
        var detailFlag = [[${@permission.hasPermi('ads:offer:query')}]];
        var statusDatas = [[${@dict.getType('sys_normal_disable')}]];
        var prefix = ctx + "ads/offer";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: "/ads/offer/add",
                updateUrl: "/ads/offer/edit/{id}",
                detailUrl: "/ads/offer/detail/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "广告配置",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'offerId',
                    title: '配置ID',
                    visible: false
                },
                {
                    field: 'adsName',
                    title: '广告名称',
                    sortable: true
                },
                {
                    field: 'advertiserName',
                    title: '广告主',
                    sortable: true
                },
                {
                    field: 'publisherName',
                    title: '开发者',
                    sortable: true
                },
                {
                    field: 'payout',
                    title: '单价($)',
                    sortable: true,
                    formatter: function(value, row, index) {
                        // return value ? '$' + value.toFixed(2) : '$0.00';
                        // 先判断是否为有效数字
                        if (value !== null && value !== undefined && !isNaN(value)) {
                            const num = parseFloat(value);
                            if (!isNaN(num)) {
                                return '$' + num.toFixed(2);
                            }
                        }
                        return '$0.00';
                    }
                },
                {
                    field: 'country',
                    title: '投放国家'
                },
                {
                    field: 'installCap',
                    title: '安装上限',
                    formatter: function(value, row, index) {
                        return value || '无限制';
                    }
                },
                {
                    field: 'dailyCap',
                    title: '日上限',
                    formatter: function(value, row, index) {
                        return value || '无限制';
                    }
                },
                {
                    field: 'priority',
                    title: '优先级',
                    sortable: true
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.offerId + '\')"><i class="fa fa-search-plus"></i>查看</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.offerId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="viewClickUrl(\'' + row.offerId + '\')"><i class="fa fa-link"></i>点击链接</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.offerId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 加载广告主列表
            $.get(ctx + "ads/offer/advertiserList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchAdvertiserId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });

            // 加载开发者列表
            $.get(ctx + "ads/offer/publisherList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchPublisherId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });
        });

        // 查看点击链接
        function viewClickUrl(offerId) {
            $.get(prefix + "/" + offerId, function(result) {
                if (result.code == web_status.SUCCESS) {
                    var clickUrl = result.data.clickUrl;
                    var content = '<div class="form-group">' +
                                 '<label>点击链接：</label>' +
                                 '<div class="input-group">' +
                                 '<input type="text" class="form-control" value="' + clickUrl + '" readonly>' +
                                 '<span class="input-group-btn">' +
                                 '<button class="btn btn-default" type="button" onclick="copyToClipboard(\'' + clickUrl + '\')">复制</button>' +
                                 '</span>' +
                                 '</div>' +
                                 '</div>';
                    $.modal.open("点击链接", content);
                }
            });
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                $.modal.alertSuccess("链接已复制到剪贴板！");
            });
        }

        // 批量创建
        function batchCreate() {
            var url = prefix + "/batchAdd";
            $.modal.open("批量创建广告配置", url);
        }
    </script>
</body>
</html>
