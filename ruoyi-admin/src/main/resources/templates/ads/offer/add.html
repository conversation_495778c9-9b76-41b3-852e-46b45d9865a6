<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增广告配置')" />
    <th:block th:include="include :: select2-css" />
    <style>
        .compact-form .form-group {
            margin-bottom: 15px;
        }
        .compact-form .form-group.compact {
            margin-bottom: 10px;
        }
        .compact-form .row {
            margin-bottom: 10px;
        }
        .compact-form .col-sm-6 {
            padding-left: 8px;
            padding-right: 8px;
        }
        .compact-form .control-label {
            font-weight: 600;
            color: #495057;
        }
        .number-input {
            width: 120px;
            display: inline-block;
        }
        .panel {
            margin-bottom: 20px;
        }
        .panel-heading {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .panel-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal compact-form" id="form-offer-add">
            <!-- 基本信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info-circle"></i> 基本信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                        <div class="form-group compact">
                            <label class="col-sm-4 control-label is-required">广告主：</label>
                            <div class="col-sm-8">
                                <select id="advertiserId" name="advertiserId" class="form-control" required>
                                    <option value="">请选择广告主</option>
                                </select>
                            </div>
                        </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">开发者：</label>
                                <div class="col-sm-8">
                                    <select id="publisherId" name="publisherId" class="form-control" required>
                                        <option value="">请选择开发者</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">广告：</label>
                                <div class="col-sm-8">
                                    <select id="adsId" name="adsId" class="form-control" required>
                                        <option value="">请先选择广告主</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">投放国家：</label>
                                <div class="col-sm-8">
                                    <input name="country" class="form-control" type="text" placeholder="多个国家用逗号分隔，如：US,UK,CA">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">单价($)：</label>
                                <div class="col-sm-8">
                                    <input name="payout" class="form-control number-input" type="number" min="0" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 限制设置 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-cogs"></i> 限制设置</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group compact">
                                <label class="col-sm-6 control-label is-required">安装上限：</label>
                                <div class="col-sm-6">
                                    <input name="installCap" class="form-control number-input" type="number" min="0" value="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group compact">
                                <label class="col-sm-6 control-label is-required">日上限：</label>
                                <div class="col-sm-6">
                                    <input name="dailyCap" class="form-control number-input" type="number" min="0" value="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group compact">
                                <label class="col-sm-6 control-label">优先级：</label>
                                <div class="col-sm-6">
                                    <input name="priority" class="form-control number-input" type="number" min="1" value="1">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 链接信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-link"></i> 链接信息</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">点击链接：</label>
                        <div class="col-sm-10">
                            <input name="clickUrl" class="form-control" type="text" readonly>
                            <span class="help-block m-b-none">
                                <i class="fa fa-info-circle"></i>
                                点击链接将在保存配置后自动生成
                            </span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">预览链接：</label>
                        <div class="col-sm-10">
                            <input name="previewUrl" class="form-control" type="text">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info"></i> 其他信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">KPI：</label>
                                <div class="col-sm-8">
                                    <input name="kpi" class="form-control" type="text" >
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">状态：</label>
                                <div class="col-sm-8">
                                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                                        <input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                                        <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注：</label>
                        <div class="col-sm-10">
                            <textarea name="remark" class="form-control" rows="2"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "ads/offer";
        $("#form-offer-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-offer-add').serialize());
            }
        }

        $(function() {
            // 加载广告主列表
            $.get(prefix + "/advertiserList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#advertiserId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });

            // 加载开发者列表
            $.get(prefix + "/publisherList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#publisherId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });

            // 广告主选择变化时加载对应的广告列表
            $("#advertiserId").change(function() {
                var advertiserId = $(this).val();
                var $adsSelect = $("#adsId");
                $adsSelect.empty().append('<option value="">请选择广告</option>');
                
                if (advertiserId) {
                    $.get(prefix + "/adsByAdvertiser/" + advertiserId, function(result) {
                        if (result.code == web_status.SUCCESS) {
                            var data = result.data;
                            for (var i = 0; i < data.length; i++) {
                                $adsSelect.append('<option value="' + data[i].adsId + '">' + data[i].adsName + '</option>');
                            }
                        }
                    });
                }
                
                // 清空点击链接
                $("input[name='clickUrl']").val("");
            });

            // 开发者或广告选择变化时生成点击链接
            $("#publisherId, #adsId").change(function() {
                // generateClickUrl();
            });

            // 广告和开发者组合唯一性验证
            $("#adsId, #publisherId").blur(function() {
                var adsId = $("#adsId").val();
                var publisherId = $("#publisherId").val();
                if (adsId && publisherId) {
                    $.post(prefix + "/checkAdsPublisherUnique", { "adsId": adsId, "publisherId": publisherId }, function(result) {
                        if (result == "1") {
                            $.modal.alertWarning("该广告已配置给此开发者！");
                            $("#adsId").focus();
                        }
                    });
                }
            });
        });


    </script>
</body>
</html>
