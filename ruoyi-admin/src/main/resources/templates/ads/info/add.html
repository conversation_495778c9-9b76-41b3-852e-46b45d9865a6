<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增广告信息')" />
    <th:block th:include="include :: select2-css" />
    <style>
        .compact-form .form-group {
            margin-bottom: 15px;
        }
        .compact-form .form-group.compact {
            margin-bottom: 10px;
        }
        .compact-form .row {
            margin-bottom: 10px;
        }
        .compact-form .col-sm-6 {
            padding-left: 8px;
            padding-right: 8px;
        }
        .compact-form .control-label {
            font-weight: 600;
            color: #495057;
        }
        .number-input {
            width: 120px;
            display: inline-block;
        }
        .panel {
            margin-bottom: 20px;
        }
        .panel-heading {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .panel-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal compact-form" id="form-info-add">
            <!-- 基本信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info-circle"></i> 基本信息</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">广告主：</label>
                        <div class="col-sm-8">
                            <select id="advertiserId" name="advertiserId" class="form-control" required>
                                <option value="">请选择广告主</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">广告名称：</label>
                                <div class="col-sm-8">
                                    <input name="adsName" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label is-required">应用ID：</label>
                                <div class="col-sm-8">
                                    <input name="appId" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">应用名称：</label>
                                <div class="col-sm-8">
                                    <input name="appName" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">包名：</label>
                                <div class="col-sm-8">
                                    <input name="packageName" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">应用分类：</label>
                                <div class="col-sm-8">
                                    <select name="category" class="form-control">
                                        <option value="">请选择分类</option>
                                        <option value="Game">游戏</option>
                                        <option value="Social">社交</option>
                                        <option value="Entertainment">娱乐</option>
                                        <option value="Lifestyle">生活</option>
                                        <option value="Utilities">工具</option>
                                        <option value="Finance">金融</option>
                                        <option value="Shopping">购物</option>
                                        <option value="Education">教育</option>
                                        <option value="Health">健康</option>
                                        <option value="Travel">旅行</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">投放国家：</label>
                                <div class="col-sm-8">
                                    <input name="country" class="form-control" type="text" placeholder="多个国家用逗号分隔，如：US,UK,CA">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 链接信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-link"></i> 链接信息</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">图标URL：</label>
                        <div class="col-sm-10">
                            <input name="iconUrl" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">点击链接：</label>
                        <div class="col-sm-10">
                            <input name="clickUrl" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">预览链接：</label>
                        <div class="col-sm-10">
                            <input name="previewUrl" class="form-control" type="text">
                        </div>
                    </div>
                </div>
            </div>
            <!-- 限制设置 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-cogs"></i> 限制设置</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group compact">
                                <label class="col-sm-6 control-label">点击上限：</label>
                                <div class="col-sm-6">
                                    <input name="clickCap" class="form-control number-input" type="number" min="0" placeholder="无限制">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group compact">
                                <label class="col-sm-6 control-label">安装上限：</label>
                                <div class="col-sm-6">
                                    <input name="installCap" class="form-control number-input" type="number" min="0" placeholder="无限制">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group compact">
                                <label class="col-sm-6 control-label">转化上限：</label>
                                <div class="col-sm-6">
                                    <input name="conversionCap" class="form-control number-input" type="number" min="0" placeholder="无限制">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info"></i> 其他信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">KPI要求：</label>
                                <div class="col-sm-8">
                                    <input name="kpi" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">转化事件：</label>
                                <div class="col-sm-8">
                                    <input name="conversionEvent" class="form-control" type="text" placeholder="如：install, register, purchase">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">广告描述：</label>
                        <div class="col-sm-10">
                            <textarea name="description" class="form-control" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">状态：</label>
                                <div class="col-sm-8">
                                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                                        <input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                                        <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">备注：</label>
                                <div class="col-sm-8">
                                    <textarea name="remark" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "ads/info";
        $("#form-info-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-info-add').serialize());
            }
        }

        $(function() {
            // 加载广告主列表
            $.get(ctx + "ads/partner/advertiserList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#advertiserId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });

            // 广告名称唯一性验证
            $("input[name='adsName']").blur(function() {
                var adsName = $(this).val();
                if (adsName != '') {
                    $.post(prefix + "/checkAdsNameUnique", { "adsName": adsName }, function(result) {
                        if (result == "1") {
                            $.modal.alertWarning("广告名称已存在！");
                            $("input[name='adsName']").focus();
                        }
                    });
                }
            });

            // 应用ID唯一性验证
            $("input[name='appId']").blur(function() {
                var appId = $(this).val();
                if (appId != '') {
                    $.post(prefix + "/checkAppIdUnique", { "appId": appId }, function(result) {
                        if (result == "1") {
                            $.modal.alertWarning("应用ID已存在！");
                            $("input[name='appId']").focus();
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>
