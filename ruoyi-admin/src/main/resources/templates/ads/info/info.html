<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('广告信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>广告名称：</label>
                                <input type="text" name="adsName"/>
                            </li>
                            <li>
                                <label>应用ID：</label>
                                <input type="text" name="appId"/>
                            </li>
                            <li>
                                <label>广告主：</label>
                                <select name="advertiserId" th:with="type=${@dict.getType('ads_advertiser')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ads:info:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-info single disabled" onclick="$.operate.detail()" shiro:hasPermission="ads:info:query">
                    <i class="fa fa-search-plus"></i> 查看
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ads:info:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="ads:info:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ads:info:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="ads:info:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('ads:info:edit')}]];
        var removeFlag = [[${@permission.hasPermi('ads:info:remove')}]];
        var detailFlag = [[${@permission.hasPermi('ads:info:query')}]];
        var statusDatas = [[${@dict.getType('sys_normal_disable')}]];
        var prefix = ctx + "ads/info";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: "/ads/info/add",
                updateUrl: "/ads/info/edit/{id}",
                detailUrl: "/ads/info/detail/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "广告信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'adsId',
                    title: '广告ID',
                    visible: false
                },
                {
                    field: 'adsName',
                    title: '广告名称',
                    sortable: true
                },
                {
                    field: 'appId',
                    title: '应用ID',
                    sortable: true
                },
                {
                    field: 'appName',
                    title: '应用名称'
                },
                {
                    field: 'packageName',
                    title: '包名'
                },
                {
                    field: 'country',
                    title: '投放国家'
                },
                {
                    field: 'category',
                    title: '应用分类'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.adsId + '\')"><i class="fa fa-search-plus"></i>查看</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.adsId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.adsId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
