<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('点击统计')" />
<!--    <th:block th:include="include :: echarts-css" />-->
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>广告主：</label>
                                <select name="advertiserId" id="searchAdvertiserId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>开发者：</label>
                                <select name="publisherId" id="searchPublisherId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>统计时间：</label>
                                <input type="text" class="time-input" placeholder="请选择开始时间" name="beginTime" id="beginTime"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="请选择结束时间" name="endTime" id="endTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="loadStatistics()"><i class="fa fa-search"></i>&nbsp;查询</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="resetForm()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="">
            <div class="col-sm-3">
                <div class="ibox">
                    <div class="ibox-content">
                        <h5>总点击数</h5>
                        <h2 class="text-primary" id="totalClicks">0</h2>
                        <small>累计点击次数</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox">
                    <div class="ibox-content">
                        <h5>今日点击</h5>
                        <h2 class="text-success" id="todayClicks">0</h2>
                        <small>今日新增点击</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox">
                    <div class="ibox-content">
                        <h5>昨日点击</h5>
                        <h2 class="text-info" id="yesterdayClicks">0</h2>
                        <small>昨日点击次数</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox">
                    <div class="ibox-content">
                        <h5>平均单价</h5>
                        <h2 class="text-warning" id="avgPayout">$0.00</h2>
                        <small>平均点击单价</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>点击趋势图</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div id="clickTrendChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="">
            <div class="col-sm-6">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>广告主点击分布</h5>
                    </div>
                    <div class="ibox-content">
                        <div id="advertiserChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>国家分布</h5>
                    </div>
                    <div class="ibox-content">
                        <div id="countryChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>详细统计数据</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-xs" onclick="exportStatistics()">
                                <i class="fa fa-download"></i> 导出
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table id="statisticsTable" class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>统计日期</th>
                                    <th>广告主</th>
                                    <th>开发者</th>
                                    <th>广告名称</th>
                                    <th>国家</th>
                                    <th>渠道</th>
                                    <th>点击数</th>
                                    <th>独立设备数</th>
                                    <th>独立IP数</th>
                                    <th>总收入</th>
                                    <th>平均单价</th>
                                </tr>
                            </thead>
                            <tbody id="statisticsTableBody">
                                <tr>
                                    <td colspan="11" class="text-center">暂无数据</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: echarts-js" />
    <script th:inline="javascript">
        var clickTrendChart, advertiserChart, countryChart;

        $(function() {
            // 初始化图表
            initCharts();
            
            // 加载广告主和开发者列表
            loadPartnerLists();
            
            // 设置默认时间范围（最近7天）
            setDefaultTimeRange();
            
            // 加载初始统计数据
            loadStatistics();
        });

        // 初始化图表
        function initCharts() {
            clickTrendChart = echarts.init(document.getElementById('clickTrendChart'));
            advertiserChart = echarts.init(document.getElementById('advertiserChart'));
            countryChart = echarts.init(document.getElementById('countryChart'));
        }

        // 加载合作伙伴列表
        function loadPartnerLists() {
            // 加载广告主列表
            $.get(ctx + "ads/click/record/advertiserList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchAdvertiserId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });

            // 加载开发者列表
            $.get(ctx + "ads/click/record/publisherList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchPublisherId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });
        }

        // 设置默认时间范围
        function setDefaultTimeRange() {
            var endDate = new Date();
            var startDate = new Date();
            startDate.setDate(endDate.getDate() - 7);
            
            $("#beginTime").val(formatDate(startDate));
            $("#endTime").val(formatDate(endDate));
        }

        // 格式化日期
        function formatDate(date) {
            var year = date.getFullYear();
            var month = (date.getMonth() + 1).toString().padStart(2, '0');
            var day = date.getDate().toString().padStart(2, '0');
            return year + '-' + month + '-' + day;
        }

        // 加载统计数据
        function loadStatistics() {
            var params = {
                advertiserId: $("#searchAdvertiserId").val(),
                publisherId: $("#searchPublisherId").val(),
                beginTime: $("#beginTime").val(),
                endTime: $("#endTime").val()
            };

            // 显示加载状态
            showLoading();

            // 调用实际的统计API
            $.ajax({
                url: ctx + "ads/statistics/click/data",
                type: "GET",
                data: params,
                dataType: "json",
                success: function(result) {
                    hideLoading();
                    if (result.code == web_status.SUCCESS) {
                        loadRealData(result.data);
                    } else {
                        $.modal.alertError("加载统计数据失败：" + result.msg);
                    }
                },
                error: function() {
                    hideLoading();
                    $.modal.alertError("加载统计数据失败，请稍后重试");
                }
            });
        }

        // 加载真实数据
        function loadRealData(data) {
            // 更新统计卡片
            var overview = data.overview || {};
            $("#totalClicks").text((overview.totalClicks || 0).toLocaleString());
            $("#todayClicks").text((overview.todayClicks || 0).toLocaleString());
            $("#yesterdayClicks").text((overview.yesterdayClicks || 0).toLocaleString());
            $("#avgPayout").text("$" + (overview.avgPayout || 0).toFixed(4));

            // 更新趋势图
            updateClickTrendChart(data.trendData || []);

            // 更新分布图
            updateAdvertiserChart(data.distribution.advertiser || []);
            updateCountryChart(data.distribution.country || []);

            // 更新数据表格
            updateStatisticsTable(data.tableData || {});
        }

        // 更新点击趋势图
        function updateClickTrendChart(trendData) {
            trendData = trendData || [];
            var dates = [];
            var clicks = [];

            trendData.forEach(function(item) {
                dates.push(item.date);
                clicks.push(item.clicks || 0);
            });

            var option = {
                title: {
                    text: '点击趋势'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['点击数']
                },
                xAxis: {
                    type: 'category',
                    data: dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '点击数',
                        type: 'line',
                        data: clicks,
                        smooth: true
                    }
                ]
            };
            clickTrendChart.setOption(option);
        }

        // 更新广告主分布图
        function updateAdvertiserChart(advertiserData) {
            advertiserData = advertiserData || [];
            var chartData = advertiserData.map(function(item) {
                return {
                    value: item.clickCount || 0,
                    name: item.advertiserName || '未知广告主'
                };
            });

            var option = {
                title: {
                    text: '广告主点击分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                series: [
                    {
                        name: '点击数',
                        type: 'pie',
                        radius: '50%',
                        data: chartData
                    }
                ]
            };
            advertiserChart.setOption(option);
        }

        // 更新国家分布图
        function updateCountryChart(countryData) {
            countryData = countryData || [];
            var chartData = countryData.map(function(item) {
                return {
                    value: item.clickCount || 0,
                    name: item.country || '未知'
                };
            });

            var option = {
                title: {
                    text: '国家分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                series: [
                    {
                        name: '点击数',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        data: chartData
                    }
                ]
            };
            countryChart.setOption(option);
        }

        // 更新统计表格
        function updateStatisticsTable(tableData) {
            var tbody = $("#statisticsTableBody");
            tbody.empty();

            if (!tableData || !tableData.rows || tableData.rows.length === 0) {
                tbody.append('<tr><td colspan="8" class="text-center">暂无数据</td></tr>');
                return;
            }

            for (var i = 0; i < tableData.rows.length; i++) {
                var row = tableData.rows[i];
                var tr = '<tr>' +
                        '<td>' + (row.statisticsDate || '-') + '</td>' +
                        '<td>' + (row.advertiserName || '-') + '</td>' +
                        '<td>' + (row.publisherName || '-') + '</td>' +
                        '<td>' + (row.adsName || '-') + '</td>' +
                        '<td>' + (row.country || '-') + '</td>' +
                        '<td>' + (row.channel || '-') + '</td>' +
                        '<td><strong>' + ((row.clickCount || 0).toLocaleString()) + '</strong></td>' +
                        '<td><strong>' + ((row.uniqueDeviceCount || 0).toLocaleString()) + '</strong></td>' +
                        '<td><strong>' + ((row.uniqueIpCount || 0).toLocaleString()) + '</strong></td>' +
                        '<td><strong class="text-primary">$' + ((row.totalPayout || 0).toFixed(2)) + '</strong></td>' +
                        '<td>$' + ((row.avgPayout || 0).toFixed(4)) + '</td>' +
                        '</tr>';
                tbody.append(tr);
            }
        }

        // 显示加载状态
        function showLoading() {
            $(".ibox-content").append('<div class="sk-spinner sk-spinner-wave"><div class="sk-rect1"></div><div class="sk-rect2"></div><div class="sk-rect3"></div><div class="sk-rect4"></div><div class="sk-rect5"></div></div>');
        }

        // 隐藏加载状态
        function hideLoading() {
            $(".sk-spinner").remove();
        }

        // 重置表单
        function resetForm() {
            $("#searchAdvertiserId").val("");
            $("#searchPublisherId").val("");
            setDefaultTimeRange();
            loadStatistics();
        }

        // 导出统计数据
        function exportStatistics() {
            $.modal.alertInfo("导出功能开发中...");
        }

        // 窗口大小改变时重新调整图表
        $(window).resize(function() {
            if (clickTrendChart) clickTrendChart.resize();
            if (advertiserChart) advertiserChart.resize();
            if (countryChart) countryChart.resize();
        });
    </script>
</body>
</html>
