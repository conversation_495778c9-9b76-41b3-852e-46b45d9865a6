<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('统计API测试')" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>统计API测试</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-6">
                                <button type="button" class="btn btn-primary" onclick="testClickStatistics()">
                                    测试点击统计API
                                </button>
                            </div>
                            <div class="col-sm-6">
                                <button type="button" class="btn btn-success" onclick="testCallbackStatistics()">
                                    测试回调统计API
                                </button>
                            </div>
                        </div>
                        
                        <div class="row" style="margin-top: 20px;">
                            <div class="col-sm-12">
                                <h4>测试结果：</h4>
                                <pre id="testResult" style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 400px; overflow-y: auto;">
点击上方按钮开始测试...
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var ctx = /*[[@{/}]]*/ "";
        
        function testClickStatistics() {
            $("#testResult").text("正在测试点击统计API...");
            
            $.ajax({
                url: ctx + "ads/statistics/click/data",
                type: "GET",
                data: {
                    beginTime: "2025-01-01",
                    endTime: "2025-01-10"
                },
                dataType: "json",
                success: function(result) {
                    $("#testResult").text("点击统计API测试结果：\n" + JSON.stringify(result, null, 2));
                },
                error: function(xhr, status, error) {
                    $("#testResult").text("点击统计API测试失败：\n" + 
                        "状态码: " + xhr.status + "\n" +
                        "错误信息: " + error + "\n" +
                        "响应内容: " + xhr.responseText);
                }
            });
        }
        
        function testCallbackStatistics() {
            $("#testResult").text("正在测试回调统计API...");
            
            $.ajax({
                url: ctx + "ads/statistics/callback/data",
                type: "GET",
                data: {
                    beginTime: "2025-01-01",
                    endTime: "2025-01-10"
                },
                dataType: "json",
                success: function(result) {
                    $("#testResult").text("回调统计API测试结果：\n" + JSON.stringify(result, null, 2));
                },
                error: function(xhr, status, error) {
                    $("#testResult").text("回调统计API测试失败：\n" + 
                        "状态码: " + xhr.status + "\n" +
                        "错误信息: " + error + "\n" +
                        "响应内容: " + xhr.responseText);
                }
            });
        }
    </script>
</body>
</html>
