<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('回调统计')" />
<!--    <th:block th:include="include :: echarts-css" />-->
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>广告主：</label>
                                <select name="advertiserId" id="searchAdvertiserId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>开发者：</label>
                                <select name="publisherId" id="searchPublisherId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>回调类型：</label>
                                <select name="callbackType">
                                    <option value="">所有</option>
                                    <option value="install">安装</option>
                                    <option value="event">事件</option>
                                </select>
                            </li>
                            <li>
                                <label>统计时间：</label>
                                <input type="text" class="time-input" placeholder="请选择开始时间" name="beginTime" id="beginTime"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="请选择结束时间" name="endTime" id="endTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="loadStatistics()"><i class="fa fa-search"></i>&nbsp;查询</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="resetForm()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="">
            <div class="col-sm-3">
                <div class="ibox">
                    <div class="ibox-content">
                        <h5>总回调数</h5>
                        <h2 class="text-primary" id="totalCallbacks">0</h2>
                        <small>累计回调次数</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox">
                    <div class="ibox-content">
                        <h5>成功回调</h5>
                        <h2 class="text-success" id="successCallbacks">0</h2>
                        <small>成功回调次数</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox">
                    <div class="ibox-content">
                        <h5>失败回调</h5>
                        <h2 class="text-danger" id="failedCallbacks">0</h2>
                        <small>失败回调次数</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox">
                    <div class="ibox-content">
                        <h5>成功率</h5>
                        <h2 class="text-info" id="successRate">0%</h2>
                        <small>回调成功率</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>回调趋势图</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div id="callbackTrendChart" style="height: 400px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="">
            <div class="col-sm-6">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>回调类型分布</h5>
                    </div>
                    <div class="ibox-content">
                        <div id="callbackTypeChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>回调状态分布</h5>
                    </div>
                    <div class="ibox-content">
                        <div id="callbackStatusChart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>详细统计数据</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-xs" onclick="exportStatistics()">
                                <i class="fa fa-download"></i> 导出
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table id="statisticsTable" class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>统计日期</th>
                                    <th>回调类型</th>
                                    <th>广告主</th>
                                    <th>开发者</th>
                                    <th>广告名称</th>
                                    <th>总回调数</th>
                                    <th>成功数</th>
                                    <th>失败数</th>
                                    <th>成功率</th>
                                </tr>
                            </thead>
                            <tbody id="statisticsTableBody">
                                <tr>
                                    <td colspan="9" class="text-center">暂无数据</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: echarts-js" />
    <script th:inline="javascript">
        var callbackTrendChart, callbackTypeChart, callbackStatusChart;

        $(function() {
            // 初始化图表
            initCharts();
            
            // 加载广告主和开发者列表
            loadPartnerLists();
            
            // 设置默认时间范围（最近7天）
            setDefaultTimeRange();
            
            // 加载初始统计数据
            loadStatistics();
        });

        // 初始化图表
        function initCharts() {
            callbackTrendChart = echarts.init(document.getElementById('callbackTrendChart'));
            callbackTypeChart = echarts.init(document.getElementById('callbackTypeChart'));
            callbackStatusChart = echarts.init(document.getElementById('callbackStatusChart'));
        }

        // 加载合作伙伴列表
        function loadPartnerLists() {
            // 加载广告主列表
            $.get(ctx + "ads/callback/record/advertiserList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchAdvertiserId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });

            // 加载开发者列表
            $.get(ctx + "ads/callback/record/publisherList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchPublisherId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });
        }

        // 设置默认时间范围
        function setDefaultTimeRange() {
            var endDate = new Date();
            var startDate = new Date();
            startDate.setDate(endDate.getDate() - 7);
            
            $("#beginTime").val(formatDate(startDate));
            $("#endTime").val(formatDate(endDate));
        }

        // 格式化日期
        function formatDate(date) {
            var year = date.getFullYear();
            var month = (date.getMonth() + 1).toString().padStart(2, '0');
            var day = date.getDate().toString().padStart(2, '0');
            return year + '-' + month + '-' + day;
        }

        // 加载统计数据
        function loadStatistics() {
            var params = {
                advertiserId: $("#searchAdvertiserId").val(),
                publisherId: $("#searchPublisherId").val(),
                callbackType: $("select[name='callbackType']").val(),
                beginTime: $("#beginTime").val(),
                endTime: $("#endTime").val()
            };

            // 显示加载状态
            showLoading();

            // 调用实际的统计API
            $.ajax({
                url: ctx + "ads/statistics/callback/data",
                type: "GET",
                data: params,
                dataType: "json",
                success: function(result) {
                    hideLoading();
                    if (result.code == web_status.SUCCESS) {
                        loadRealData(result.data);
                    } else {
                        $.modal.alertError("加载统计数据失败：" + result.msg);
                    }
                },
                error: function() {
                    hideLoading();
                    $.modal.alertError("加载统计数据失败，请稍后重试");
                }
            });
        }

        // 加载真实数据
        function loadRealData(data) {
            // 更新统计卡片
            var overview = data.overview || {};
            $("#totalCallbacks").text((overview.totalCallbacks || 0).toLocaleString());
            $("#successCallbacks").text((overview.successCallbacks || 0).toLocaleString());
            $("#failedCallbacks").text((overview.failedCallbacks || 0).toLocaleString());
            $("#successRate").text(overview.successRate || "0.0%");

            // 更新趋势图
            updateCallbackTrendChart(data.trendData || []);

            // 更新分布图
            updateCallbackTypeChart(data.distribution.type || []);
            updateCallbackStatusChart(data.distribution.status || []);

            // 更新数据表格
            updateStatisticsTable(data.tableData || {});
        }

        // 更新回调趋势图
        function updateCallbackTrendChart(trendData) {
            trendData = trendData || [];
            var dates = [];
            var totalData = [];
            var successData = [];

            trendData.forEach(function(item) {
                dates.push(item.date);
                totalData.push(item.total || 0);
                successData.push(item.success || 0);
            });

            var option = {
                title: {
                    text: '回调趋势'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['总回调数', '成功回调']
                },
                xAxis: {
                    type: 'category',
                    data: dates
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '总回调数',
                        type: 'line',
                        data: totalData,
                        smooth: true
                    },
                    {
                        name: '成功回调',
                        type: 'line',
                        data: successData,
                        smooth: true
                    }
                ]
            };
            callbackTrendChart.setOption(option);
        }

        // 更新回调类型分布图
        function updateCallbackTypeChart(typeData) {
            typeData = typeData || [];
            var chartData = typeData.map(function(item) {
                var typeName = item.callbackType;
                if (typeName === 'install') {
                    typeName = '安装回调';
                } else if (typeName === 'event') {
                    typeName = '事件回调';
                }
                return {
                    value: item.callbackCount || 0,
                    name: typeName
                };
            });

            var option = {
                title: {
                    text: '回调类型分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                series: [
                    {
                        name: '回调数',
                        type: 'pie',
                        radius: '50%',
                        data: chartData
                    }
                ]
            };
            callbackTypeChart.setOption(option);
        }

        // 更新回调状态分布图
        function updateCallbackStatusChart(statusData) {
            statusData = statusData || [];
            var chartData = statusData.map(function(item) {
                var statusName = '未知';
                if (item.status == 0) {
                    statusName = '待处理';
                } else if (item.status == 1) {
                    statusName = '成功';
                } else if (item.status == 2) {
                    statusName = '失败';
                }
                return {
                    value: item.callbackCount || 0,
                    name: statusName
                };
            });

            var option = {
                title: {
                    text: '回调状态分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                series: [
                    {
                        name: '回调数',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        data: chartData
                    }
                ]
            };
            callbackStatusChart.setOption(option);
        }

        // 更新统计表格
        function updateStatisticsTable(tableData) {
            var tbody = $("#statisticsTableBody");
            tbody.empty();

            if (!tableData || !tableData.rows || tableData.rows.length === 0) {
                tbody.append('<tr><td colspan="9" class="text-center">暂无数据</td></tr>');
                return;
            }

            for (var i = 0; i < tableData.rows.length; i++) {
                var row = tableData.rows[i];
                var callbackTypeName = row.callbackType;
                if (callbackTypeName === 'install') {
                    callbackTypeName = '安装回调';
                } else if (callbackTypeName === 'event') {
                    callbackTypeName = '事件回调';
                }

                var successRate = 0;
                if (row.totalCallbacks > 0) {
                    successRate = ((row.successCallbacks || 0) / row.totalCallbacks * 100).toFixed(1);
                }

                var tr = '<tr>' +
                        '<td>' + (row.statisticsDate || '-') + '</td>' +
                        '<td>' + (callbackTypeName || '-') + '</td>' +
                        '<td>' + (row.advertiserName || '-') + '</td>' +
                        '<td>' + (row.publisherName || '-') + '</td>' +
                        '<td>' + (row.adsName || '-') + '</td>' +
                        '<td><strong>' + ((row.totalCallbacks || 0).toLocaleString()) + '</strong></td>' +
                        '<td><strong class="text-success">' + ((row.successCallbacks || 0).toLocaleString()) + '</strong></td>' +
                        '<td><strong class="text-danger">' + ((row.failedCallbacks || 0).toLocaleString()) + '</strong></td>' +
                        '<td><span class="badge bg-info">' + successRate + '%</span></td>' +
                        '</tr>';
                tbody.append(tr);
            }
        }

        // 显示加载状态
        function showLoading() {
            $(".ibox-content").append('<div class="sk-spinner sk-spinner-wave"><div class="sk-rect1"></div><div class="sk-rect2"></div><div class="sk-rect3"></div><div class="sk-rect4"></div><div class="sk-rect5"></div></div>');
        }

        // 隐藏加载状态
        function hideLoading() {
            $(".sk-spinner").remove();
        }

        // 重置表单
        function resetForm() {
            $("#searchAdvertiserId").val("");
            $("#searchPublisherId").val("");
            $("select[name='callbackType']").val("");
            setDefaultTimeRange();
            loadStatistics();
        }

        // 导出统计数据
        function exportStatistics() {
            $.modal.alertInfo("导出功能开发中...");
        }

        // 窗口大小改变时重新调整图表
        $(window).resize(function() {
            if (callbackTrendChart) callbackTrendChart.resize();
            if (callbackTypeChart) callbackTypeChart.resize();
            if (callbackStatusChart) callbackStatusChart.resize();
        });
    </script>
</body>
</html>
