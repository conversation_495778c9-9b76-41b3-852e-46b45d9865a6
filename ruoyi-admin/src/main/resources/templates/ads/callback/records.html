<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('回调记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>点击ID：</label>
                                <input type="text" name="clickId"/>
                            </li>
                            <li>
                                <label>回调类型：</label>
                                <select name="callbackType">
                                    <option value="">所有</option>
                                    <option value="install">安装</option>
                                    <option value="event">事件</option>
                                </select>
                            </li>
                            <li>
                                <label>事件名称：</label>
                                <input type="text" name="eventName"/>
                            </li>
                            <li>
                                <label>广告主：</label>
                                <select name="advertiserId" id="searchAdvertiserId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>开发者：</label>
                                <select name="publisherId" id="searchPublisherId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>回调状态：</label>
                                <select name="callbackStatus">
                                    <option value="">所有</option>
                                    <option value="0">待处理</option>
                                    <option value="1">成功</option>
                                    <option value="2">失败</option>
                                </select>
                            </li>
                            <li>
                                <label>回调时间：</label>
                                <input type="text" class="time-input" placeholder="请选择开始时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="请选择结束时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ads:callback:records">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="showStatistics()">
                    <i class="fa fa-bar-chart"></i> 统计
                </a>
                <a class="btn btn-success" onclick="retryFailedCallbacks()" shiro:hasPermission="ads:callback:records">
                    <i class="fa fa-refresh"></i> 重试失败回调
                </a>
                <a class="btn btn-danger" onclick="cleanExpiredRecords()" shiro:hasPermission="ads:callback:records">
                    <i class="fa fa-trash"></i> 清理过期记录
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "ads/callback/record";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                detailUrl: "ads/callback/detail/{id}",
                modalName: "回调记录",
                showRefresh: true,
                showToggle: true,
                showColumns: true,
                rememberSelected: true,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'recordId',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'clickId',
                    title: '点击ID',
                    sortable: true,
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'callbackType',
                    title: '回调类型',
                    formatter: function(value, row, index) {
                        if (value == 'install') {
                            return '<span class="label label-primary">安装</span>';
                        } else if (value == 'event') {
                            return '<span class="label label-info">事件</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'eventName',
                    title: '事件名称'
                },
                {
                    field: 'eventValue',
                    title: '事件值'
                },
                {
                    field: 'adsName',
                    title: '广告名称',
                    sortable: true
                },
                {
                    field: 'advertiserName',
                    title: '广告主'
                },
                {
                    field: 'publisherName',
                    title: '开发者'
                },
                {
                    field: 'callbackStatus',
                    title: '回调状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == 0) {
                            return '<span class="label label-warning">待处理</span>';
                        } else if (value == 1) {
                            return '<span class="label label-success">成功</span>';
                        } else if (value == 2) {
                            return '<span class="label label-danger">失败</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'retryCount',
                    title: '重试次数'
                },
                {
                    field: 'callbackTime',
                    title: '回调时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.recordId + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.recordId + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        if (row.callbackStatus == 2) { // 失败状态可以重试
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="retryCallback(\'' + row.recordId + '\')"><i class="fa fa-refresh"></i>重试</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 加载广告主列表
            $.get(prefix + "/advertiserList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchAdvertiserId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });

            // 加载开发者列表
            $.get(prefix + "/publisherList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchPublisherId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });
        });

        // 获取状态文本
        function getStatusText(status) {
            if (status == 0) return '待处理';
            if (status == 1) return '成功';
            if (status == 2) return '失败';
            return status;
        }

        // 重试单个回调
        function retryCallback(recordId) {
            $.modal.confirm("确定要重试此回调吗？", function() {
                $.post(prefix + "/retry/" + recordId, function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess("重试请求已提交！");
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        // 重试失败的回调
        function retryFailedCallbacks() {
            $.get(prefix + "/retryable", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var retryableCount = result.data.length;
                    if (retryableCount == 0) {
                        $.modal.alertInfo("没有需要重试的回调记录！");
                        return;
                    }
                    $.modal.confirm("发现 " + retryableCount + " 条可重试的回调记录，确定要批量重试吗？", function() {
                        // 这里可以实现批量重试逻辑
                        $.modal.alertSuccess("批量重试功能开发中...");
                    });
                }
            });
        }

        // 显示统计信息
        function showStatistics() {
            $.get(prefix + "/statistics", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var content = '<div class="text-center">' +
                                 '<h3>回调统计</h3>' +
                                 '<div class="row">' +
                                 '<div class="col-sm-3"><p class="text-info">总回调数：<strong>' + data.totalCallbacks + '</strong></p></div>' +
                                 '<div class="col-sm-3"><p class="text-success">成功：<strong>' + data.successCallbacks + '</strong></p></div>' +
                                 '<div class="col-sm-3"><p class="text-danger">失败：<strong>' + data.failedCallbacks + '</strong></p></div>' +
                                 '<div class="col-sm-3"><p class="text-warning">待处理：<strong>' + data.pendingCallbacks + '</strong></p></div>' +
                                 '</div>' +
                                 '</div>';
                    $.modal.open("回调统计", content);
                }
            });
        }

        // 清理过期记录
        function cleanExpiredRecords() {
            $.modal.confirm("确定要清理30天前的过期记录吗？", function() {
                $.post(prefix + "/clean/30", function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>
