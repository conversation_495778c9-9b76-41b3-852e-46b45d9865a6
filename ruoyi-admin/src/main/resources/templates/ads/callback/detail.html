<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('回调记录详情')" />
    <style>
        .detail-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .detail-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
        }
        .detail-body {
            padding: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            color: #333;
            width: 150px;
            flex-shrink: 0;
        }
        .info-value {
            color: #666;
            flex: 1;
            word-break: break-all;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .callback-type-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .url-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
        }
        .response-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="detail-card">
            <div class="detail-header">
                <h3><i class="fa fa-exchange"></i> 回调记录详情</h3>
                <p>记录ID: <span th:text="${adsCallbackRecord.recordId}"></span></p>
            </div>

            <div class="detail-body">
                <!-- 基本信息 -->
                <div class="row">
                    <div class="col-md-6">
                        <h4><i class="fa fa-info-circle"></i> 基本信息</h4>
                        <div class="info-row">
                            <div class="info-label">点击ID:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.clickId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">回调类型:</div>
                            <div class="info-value">
                                <span class="callback-type-badge" th:text="${adsCallbackRecord.callbackType == 'install' ? '安装回调' : (adsCallbackRecord.callbackType == 'event' ? '事件回调' : adsCallbackRecord.callbackType)}">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">事件名称:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.eventName}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">事件值:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.eventValue}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">配置ID:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.offerId}">-</div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h4><i class="fa fa-cogs"></i> 状态信息</h4>
                        <div class="info-row">
                            <div class="info-label">回调状态:</div>
                            <div class="info-value">
                                <span th:if="${adsCallbackRecord.callbackStatus == 0}" class="status-badge status-pending">待处理</span>
                                <span th:if="${adsCallbackRecord.callbackStatus == 1}" class="status-badge status-success">成功</span>
                                <span th:if="${adsCallbackRecord.callbackStatus == 2}" class="status-badge status-error">失败</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">响应状态码:</div>
                            <div class="info-value">
                                <span th:if="${adsCallbackRecord.responseCode != null}"
                                      th:class="${adsCallbackRecord.responseCode >= 200 && adsCallbackRecord.responseCode < 300 ? 'text-success' : 'text-danger'}"
                                      th:text="${adsCallbackRecord.responseCode}">-</span>
                                <span th:if="${adsCallbackRecord.responseCode == null}">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">重试次数:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.retryCount ?: 0}">0</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">回调时间:</div>
                            <div class="info-value" th:text="${#dates.format(adsCallbackRecord.callbackTime, 'yyyy-MM-dd HH:mm:ss')}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">记录状态:</div>
                            <div class="info-value">
                                <span th:if="${adsCallbackRecord.status == '0'}" class="status-badge status-success">正常</span>
                                <span th:if="${adsCallbackRecord.status == '1'}" class="status-badge status-error">异常</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 合作伙伴信息 -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-6">
                        <h4><i class="fa fa-users"></i> 开发者信息</h4>
                        <div class="info-row">
                            <div class="info-label">开发者ID:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.publisherId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">开发者名称:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.publisherName}">-</div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h4><i class="fa fa-building"></i> 广告主信息</h4>
                        <div class="info-row">
                            <div class="info-label">广告主ID:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.advertiserId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">广告主名称:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.advertiserName}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">广告ID:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.adsId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">广告名称:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.adsName}">-</div>
                        </div>
                    </div>
                </div>

                <!-- 回调URL -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12">
                        <h4><i class="fa fa-link"></i> 回调URL</h4>
                        <div class="info-row">
                            <div class="info-label">回调地址:</div>
                            <div class="info-value">
                                <div class="url-box" th:text="${adsCallbackRecord.callbackUrl}">-</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 回调响应 -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12">
                        <h4><i class="fa fa-reply"></i> 回调响应</h4>
                        <div class="info-row">
                            <div class="info-label">响应内容:</div>
                            <div class="info-value">
                                <div class="response-box" th:text="${adsCallbackRecord.callbackResponse ?: '无响应内容'}"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12">
                        <h4><i class="fa fa-info"></i> 系统信息</h4>
                        <div class="info-row">
                            <div class="info-label">创建时间:</div>
                            <div class="info-value" th:text="${#dates.format(adsCallbackRecord.createTime, 'yyyy-MM-dd HH:mm:ss')}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">更新时间:</div>
                            <div class="info-value" th:text="${#dates.format(adsCallbackRecord.updateTime, 'yyyy-MM-dd HH:mm:ss')}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">备注:</div>
                            <div class="info-value" th:text="${adsCallbackRecord.remark}">-</div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12 text-center">
                        <button type="button" class="btn btn-warning" onclick="retryCallback()"
                                th:if="${adsCallbackRecord.callbackStatus == 2}">
                            <i class="fa fa-refresh"></i> 重试回调
                        </button>
                        <button type="button" class="btn btn-info" onclick="viewClickRecord()">
                            <i class="fa fa-mouse-pointer"></i> 查看点击记录
                        </button>
                        <button type="button" class="btn btn-default" onclick="$.modal.close()">
                            <i class="fa fa-close"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var recordId = [[${adsCallbackRecord.recordId}]];
        var clickId = [[${adsCallbackRecord.clickId}]];

        function retryCallback() {
            $.modal.confirm("确定要重试此回调吗？", function() {
                $.post(ctx + "ads/callback/records/retry/" + recordId, function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess("重试请求已提交！");
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        function viewClickRecord() {
            var url = ctx + "ads/click/records?clickId=" + clickId;
            $.modal.openTab("点击记录", url);
        }
    </script>
</body>
</html>