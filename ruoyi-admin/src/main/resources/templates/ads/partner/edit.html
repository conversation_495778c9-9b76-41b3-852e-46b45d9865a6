<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改合作伙伴')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-partner-edit" th:object="${adsPartner}">
            <input name="partnerId" th:field="*{partnerId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">合作伙伴名称：</label>
                <div class="col-sm-8">
                    <input name="partnerName" th:field="*{partnerName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">合作伙伴密钥：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input name="partnerKey" th:field="*{partnerKey}" class="form-control" type="text" required readonly>
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button" onclick="generatePartnerKey()">
                                <i class="fa fa-refresh"></i> 重新生成
                            </button>
                        </span>
                    </div>
                    <span class="help-block m-b-none">
                        <i class="fa fa-warning text-warning"></i> 
                        重新生成密钥会影响现有的API调用，请谨慎操作！
                    </span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">合作伙伴类型：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="edit_partnerType0" name="partnerType" value="0" th:field="*{partnerType}">
                        <label for="edit_partnerType0">广告主</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="edit_partnerType1" name="partnerType" value="1" th:field="*{partnerType}">
                        <label for="edit_partnerType1">开发者</label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系人：</label>
                <div class="col-sm-8">
                    <input name="contactPerson" th:field="*{contactPerson}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系邮箱：</label>
                <div class="col-sm-8">
                    <input name="contactEmail" th:field="*{contactEmail}" class="form-control" type="email">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">联系电话：</label>
                <div class="col-sm-8">
                    <input name="contactPhone" th:field="*{contactPhone}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" id="installPostbackUrlGroup">    
                <label class="col-sm-3 control-label">安装回调URL：</label>
                <div class="col-sm-8">
                    <input name="installPostbackUrl" th:field="*{installPostbackUrl}" class="form-control" type="text" placeholder="支持宏参数：{click_id}, {device_id}, {ip_address}等">
                    <span class="help-block m-b-none">
                        <i class="fa fa-info-circle"></i> 
                        示例：https://your-domain.com/callback/install?click_id={click_id}&advid={device_id}
                    </span>
                </div>
            </div>
            <div class="form-group" id="eventPostbackUrlGroup">
                <label class="col-sm-3 control-label">事件回调URL：</label>
                <div class="col-sm-8">
                    <input name="eventPostbackUrl" th:field="*{eventPostbackUrl}" class="form-control" type="text" placeholder="支持宏参数：{click_id}, {device_id}, {event_name}, {event_value}等">
                    <span class="help-block m-b-none">
                        <i class="fa fa-info-circle"></i>
                        示例：https://your-domain.com/callback/event?click_id={click_id}&event={event_name}
                    </span>
                </div>
            </div>
            <div class="form-group" id="enableCallbackGroup">
                <label class="col-sm-3 control-label">启用回调：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="enableCallback1" name="enableCallback" value="1" th:field="*{enableCallback}">
                        <label for="enableCallback1">启用</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="enableCallback0" name="enableCallback" value="0" th:field="*{enableCallback}">
                        <label for="enableCallback0">不启用</label>
                    </div>
                    <span class="help-block m-b-none">
                        <i class="fa fa-info-circle"></i>
                        控制是否向开发者发送回调通知
                    </span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                        <input type="radio" th:id="${'edit_status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
                        <label th:for="${'edit_status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" th:field="*{remark}" class="form-control" rows="3"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "ads/partner";
        $("#form-partner-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-partner-edit').serialize());
            }
        }

        // 生成合作伙伴密钥
        function generatePartnerKey() {
            $.modal.confirm("重新生成密钥会影响现有的API调用，确定要继续吗？", function() {
                $.get(prefix + "/generatePartnerKey", function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $("input[name='partnerKey']").val(result.data);
                        $.modal.alertSuccess("密钥已重新生成！");
                    }
                });
            });
        }

        $(function() {
            // 合作伙伴名称唯一性验证
            $("input[name='partnerName']").blur(function() {
                var partnerName = $(this).val();
                var partnerId = $("input[name='partnerId']").val();
                if (partnerName != '') {
                    $.post(prefix + "/checkPartnerNameUnique", { "partnerName": partnerName, "partnerId": partnerId }, function(result) {
                        if (result == "1") {
                            $.modal.alertWarning("合作伙伴名称已存在！");
                            $("input[name='partnerName']").focus();
                        }
                    });
                }
            });

            // 合作伙伴密钥唯一性验证
            $("input[name='partnerKey']").blur(function() {
                var partnerKey = $(this).val();
                var partnerId = $("input[name='partnerId']").val();
                if (partnerKey != '') {
                    $.post(prefix + "/checkPartnerKeyUnique", { "partnerKey": partnerKey, "partnerId": partnerId }, function(result) {
                        if (result == "1") {
                            $.modal.alertWarning("合作伙伴密钥已存在！");
                            $("input[name='partnerKey']").focus();
                        }
                    });
                }
            });

            // 根据合作伙伴类型显示/隐藏回调URL字段
            $("input[name='partnerType']").change(function() {
                var partnerType = $(this).val();
                if (partnerType == "1") { // 开发者
                    $("#installPostbackUrlGroup").show();
                    $("#eventPostbackUrlGroup").show();
                    $("#enableCallbackGroup").show();
                } else { // 广告主
                    $("#installPostbackUrlGroup").hide();
                    $("#eventPostbackUrlGroup").hide();
                    $("#enableCallbackGroup").hide();
                }
            });

            // 初始化显示状态
            $("input[name='partnerType']:checked").trigger('change');
        });
    </script>
</body>
</html>
