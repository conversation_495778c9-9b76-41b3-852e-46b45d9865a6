<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('合作伙伴列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>合作伙伴名称：</label>
                                <input type="text" name="partnerName"/>
                            </li>
                            <li>
                                <label>合作伙伴密钥：</label>
                                <input type="text" name="partnerKey"/>
                            </li>
                            <li>
                                <label>合作伙伴类型：</label>
                                <select name="partnerType">
                                    <option value="">所有</option>
                                    <option value="0">广告主</option>
                                    <option value="1">开发者</option>
                                </select>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>创建时间：</label>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="请选择创建时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ads:partner:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-info single disabled" onclick="$.operate.detail()" shiro:hasPermission="ads:partner:query">
                    <i class="fa fa-search-plus"></i> 查看
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ads:partner:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="ads:partner:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ads:partner:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="$.table.importExcel()" shiro:hasPermission="ads:partner:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('ads:partner:edit')}]];
        var removeFlag = [[${@permission.hasPermi('ads:partner:remove')}]];
        var detailFlag = [[${@permission.hasPermi('ads:partner:query')}]];
        var statusDatas = [[${@dict.getType('sys_normal_disable')}]];
        var prefix = ctx + "ads/partner";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: "/ads/partner/add",
                updateUrl: "/ads/partner/edit/{id}",
                detailUrl: "/ads/partner/detail/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                importUrl: prefix + "/importData",
                importTemplateUrl: prefix + "/importTemplate",
                modalName: "合作伙伴",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'partnerId',
                    title: '合作伙伴ID',
                    visible: false
                },
                {
                    field: 'partnerName',
                    title: '合作伙伴名称',
                    sortable: true
                },
                {
                    field: 'partnerKey',
                    title: '合作伙伴密钥',
                    sortable: true,
                    formatter: function(value, row, index) {
                        return '<span title="' + value + '">' + value.substring(0, 16) + '...</span>';
                    }
                },
                {
                    field: 'partnerType',
                    title: '合作伙伴类型',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == 0) {
                            return '<span class="label label-primary">广告主</span>';
                        } else if (value == 1) {
                            return '<span class="label label-success">开发者</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'contactPerson',
                    title: '联系人'
                },
                {
                    field: 'contactEmail',
                    title: '联系邮箱'
                },
                {
                    field: 'contactPhone',
                    title: '联系电话'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs ' + detailFlag + '" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.partnerId + '\')"><i class="fa fa-search-plus"></i>查看</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.partnerId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.partnerId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
