<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增合作伙伴')" />
    <style>
        .compact-form .form-group {
            margin-bottom: 15px;
        }
        .compact-form .form-group.compact {
            margin-bottom: 10px;
        }
        .compact-form .row {
            margin-bottom: 10px;
        }
        .compact-form .col-sm-6 {
            padding-left: 8px;
            padding-right: 8px;
        }
        .compact-form .control-label {
            font-weight: 600;
            color: #495057;
        }
        .panel {
            margin-bottom: 20px;
        }
        .panel-heading {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .panel-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal compact-form" id="form-partner-add">
            <!-- 基本信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info-circle"></i> 基本信息</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">合作伙伴名称：</label>
                        <div class="col-sm-8">
                            <input name="partnerName" class="form-control" type="text" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">合作伙伴密钥：</label>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <input name="partnerKey" class="form-control" type="text" required >
                                <span class="input-group-btn">
                                    <button class="btn btn-default" type="button" onclick="generatePartnerKey()">
                                        <i class="fa fa-refresh"></i> 生成
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">合作伙伴类型：</label>
                        <div class="col-sm-8">
                            <div class="radio-box">
                                <input type="radio" id="partnerType0" name="partnerType" value="0" checked>
                                <label for="partnerType0">广告主</label>
                            </div>
                            <div class="radio-box">
                                <input type="radio" id="partnerType1" name="partnerType" value="1">
                                <label for="partnerType1">开发者</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 联系信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-user"></i> 联系信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">联系人：</label>
                                <div class="col-sm-8">
                                    <input name="contactPerson" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">联系电话：</label>
                                <div class="col-sm-8">
                                    <input name="contactPhone" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">联系邮箱：</label>
                        <div class="col-sm-10">
                            <input name="contactEmail" class="form-control" type="email">
                        </div>
                    </div>
                </div>
            </div>
            <!-- 回调配置 -->
            <div class="panel panel-default" id="callbackPanel" style="display: none;">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-link"></i> 回调配置</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group" id="installPostbackUrlGroup">
                        <label class="col-sm-2 control-label">安装回调URL：</label>
                        <div class="col-sm-10">
                            <input name="installPostbackUrl" class="form-control" type="text" placeholder="支持宏参数：{click_id}, {device_id}, {ip_address}等">
                            <span class="help-block m-b-none">
                                <i class="fa fa-info-circle"></i>
                                示例：https://your-domain.com/callback/install?click_id={click_id}&advid={device_id}
                            </span>
                        </div>
                    </div>
                    <div class="form-group" id="eventPostbackUrlGroup">
                        <label class="col-sm-2 control-label">事件回调URL：</label>
                        <div class="col-sm-10">
                            <input name="eventPostbackUrl" class="form-control" type="text" placeholder="支持宏参数：{click_id}, {device_id}, {event_name}, {event_value}等">
                            <span class="help-block m-b-none">
                                <i class="fa fa-info-circle"></i>
                                示例：https://your-domain.com/callback/event?click_id={click_id}&event={event_name}
                            </span>
                        </div>
                    </div>
                    <div class="form-group" id="enableCallbackGroup">
                        <label class="col-sm-2 control-label">启用回调：</label>
                        <div class="col-sm-10">
                            <div class="radio-box">
                                <input type="radio" id="enableCallback1" name="enableCallback" value="1" checked>
                                <label for="enableCallback1">启用</label>
                            </div>
                            <div class="radio-box">
                                <input type="radio" id="enableCallback0" name="enableCallback" value="0">
                                <label for="enableCallback0">不启用</label>
                            </div>
                            <span class="help-block m-b-none">
                                <i class="fa fa-info-circle"></i>
                                控制是否向开发者发送回调通知
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info"></i> 其他信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">状态：</label>
                                <div class="col-sm-8">
                                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                                        <input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                                        <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">备注：</label>
                                <div class="col-sm-8">
                                    <textarea name="remark" class="form-control" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "ads/partner";
        $("#form-partner-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-partner-add').serialize());
            }
        }

        // 生成合作伙伴密钥
        function generatePartnerKey() {
            $.get(prefix + "/generatePartnerKey", function(result) {
                console.log('1');
                if (result.code == web_status.SUCCESS) {
                    console.log(result.data);
                    $("input[name='partnerKey']").val(result.data);
                }
            });
        }

        $(function() {
            // 页面加载时自动生成密钥
            generatePartnerKey();

            // 合作伙伴名称唯一性验证
            $("input[name='partnerName']").blur(function() {
                var partnerName = $(this).val();
                if (partnerName != '') {
                    $.post(prefix + "/checkPartnerNameUnique", { "partnerName": partnerName }, function(result) {
                        if (result == "1") {
                            $.modal.alertWarning("合作伙伴名称已存在！");
                            $("input[name='partnerName']").focus();
                        }
                    });
                }
            });

            // 合作伙伴密钥唯一性验证
            $("input[name='partnerKey']").blur(function() {
                var partnerKey = $(this).val();
                if (partnerKey != '') {
                    $.post(prefix + "/checkPartnerKeyUnique", { "partnerKey": partnerKey }, function(result) {
                        if (result == "1") {
                            $.modal.alertWarning("合作伙伴密钥已存在！");
                            generatePartnerKey(); // 自动重新生成
                        }
                    });
                }
            });

            // 根据合作伙伴类型显示/隐藏回调URL字段
            $("input[name='partnerType']").change(function() {
                var partnerType = $(this).val();
                if (partnerType == "1") { // 开发者
                    $("#callbackPanel").show();
                    // 默认启用回调
                    $("input[name='enableCallback'][value='1']").prop('checked', true);
                } else { // 广告主
                    $("#callbackPanel").hide();
                    // 默认不启用回调
                    $("input[name='enableCallback'][value='0']").prop('checked', true);
                }
            });

            // 初始化显示状态
            $("input[name='partnerType']:checked").trigger('change');
        });
    </script>
</body>
</html>
