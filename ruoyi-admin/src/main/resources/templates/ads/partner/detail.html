<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('查看合作伙伴')" />
    <style>
        .detail-form .form-group {
            margin-bottom: 15px;
        }
        .detail-form .form-group.compact {
            margin-bottom: 10px;
        }
        .detail-form .form-control[readonly] {
            background-color: #f8f9fa;
            border-color: #e9ecef;
        }
        .detail-form .row {
            margin-bottom: 10px;
        }
        .detail-form .col-sm-6 {
            padding-left: 8px;
            padding-right: 8px;
        }
        .detail-form .control-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-form .form-control-static {
            padding-top: 7px;
            padding-bottom: 7px;
            margin-bottom: 0;
            min-height: 34px;
        }
        .badge-status {
            font-size: 12px;
            padding: 4px 8px;
        }
        .badge-type {
            font-size: 12px;
            padding: 4px 8px;
        }
        .key-display {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            word-break: break-all;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal detail-form" id="form-partner-detail">
            <!-- 基本信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info-circle"></i> 基本信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">合作伙伴ID：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static" th:text="${adsPartner.partnerId}"></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">状态：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static">
                                        <span th:class="${adsPartner.status == '0' ? 'badge badge-success badge-status' : 'badge badge-danger badge-status'}" 
                                              th:text="${adsPartner.status == '0' ? '正常' : '停用'}"></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">合作伙伴名称：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static" th:text="${adsPartner.partnerName}"></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">合作伙伴类型：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static">
                                        <span th:if="${adsPartner.partnerType == 0}" class="badge badge-primary badge-type">广告主</span>
                                        <span th:if="${adsPartner.partnerType == 1}" class="badge badge-info badge-type">开发者</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">联系人：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static" th:text="${adsPartner.contactPerson}"></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">联系邮箱：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static" th:text="${adsPartner.contactEmail}"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="col-sm-2 control-label">联系电话：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${adsPartner.contactPhone}"></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 密钥信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-key"></i> 密钥信息</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">合作伙伴密钥：</label>
                        <div class="col-sm-10">
                            <div class="key-display" th:text="${adsPartner.partnerKey}"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 回调配置 -->
            <div class="panel panel-default" th:if="${adsPartner.partnerType == 1}">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-link"></i> 回调配置</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">安装回调URL：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${adsPartner.installPostbackUrl}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">事件回调URL：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${adsPartner.eventPostbackUrl}"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">启用回调：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static">
                                <span th:if="${adsPartner.enableCallback == '1'}" class="label label-success">启用</span>
                                <span th:if="${adsPartner.enableCallback == '0'}" class="label label-danger">不启用</span>
                                <span th:if="${adsPartner.enableCallback == null}" class="label label-default">未设置</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><i class="fa fa-info"></i> 其他信息</h4>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">备注：</label>
                        <div class="col-sm-10">
                            <p class="form-control-static" th:text="${adsPartner.remark}"></p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">创建时间：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static" th:text="${#dates.format(adsPartner.createTime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group compact">
                                <label class="col-sm-4 control-label">更新时间：</label>
                                <div class="col-sm-8">
                                    <p class="form-control-static" th:text="${adsPartner.updateTime != null ? #dates.format(adsPartner.updateTime, 'yyyy-MM-dd HH:mm:ss') : ''}"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
