<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('缓存管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>缓存管理</p>
                            </li>
                            <li class="select-time">
                                <label>操作：</label>
                                <button type="button" class="btn btn-primary" onclick="refreshStats()">
                                    <i class="fa fa-refresh"></i> 刷新统计
                                </button>
                                <button type="button" class="btn btn-danger" onclick="clearAllCache()" shiro:hasPermission="ads:cache:clear">
                                    <i class="fa fa-trash"></i> 清除所有缓存
                                </button>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="ibox">
                            <div class="ibox-title">
                                <h5>缓存统计信息</h5>
                                <div class="ibox-tools">
                                    <a class="collapse-link">
                                        <i class="fa fa-chevron-up"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="ibox-content">
                                <pre id="cacheStats" style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; font-size: 12px; max-height: 400px; overflow-y: auto;">
正在加载缓存统计信息...
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-6">
                        <div class="ibox">
                            <div class="ibox-title">
                                <h5>合作伙伴缓存管理</h5>
                            </div>
                            <div class="ibox-content">
                                <div class="form-group">
                                    <label>合作伙伴ID：</label>
                                    <div class="input-group">
                                        <input type="number" id="partnerIdInput" class="form-control" placeholder="输入合作伙伴ID">
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-warning" onclick="clearPartnerCache()" shiro:hasPermission="ads:cache:clear">
                                                <i class="fa fa-trash"></i> 清除
                                            </button>
                                        </span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>合作伙伴Key：</label>
                                    <div class="input-group">
                                        <input type="text" id="partnerKeyInput" class="form-control" placeholder="输入合作伙伴Key">
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-warning" onclick="clearPartnerKeyCache()" shiro:hasPermission="ads:cache:clear">
                                                <i class="fa fa-trash"></i> 清除
                                            </button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-6">
                        <div class="ibox">
                            <div class="ibox-title">
                                <h5>广告配置缓存管理</h5>
                            </div>
                            <div class="ibox-content">
                                <div class="form-group">
                                    <label>广告ID：</label>
                                    <div class="input-group">
                                        <input type="number" id="adsIdInput" class="form-control" placeholder="输入广告ID">
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-warning" onclick="clearAdsInfoCache()" shiro:hasPermission="ads:cache:clear">
                                                <i class="fa fa-trash"></i> 清除
                                            </button>
                                        </span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>配置ID：</label>
                                    <div class="input-group">
                                        <input type="number" id="offerIdInput" class="form-control" placeholder="输入配置ID">
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-warning" onclick="clearPublisherOfferCache()" shiro:hasPermission="ads:cache:clear">
                                                <i class="fa fa-trash"></i> 发布者
                                            </button>
                                            <button type="button" class="btn btn-warning" onclick="clearAdvertiserOfferCache()" shiro:hasPermission="ads:cache:clear">
                                                <i class="fa fa-trash"></i> 广告主
                                            </button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "ads/cache";

        $(function() {
            // 页面加载时获取缓存统计信息
            refreshStats();
        });

        // 刷新缓存统计信息
        function refreshStats() {
            $.ajax({
                url: prefix + "/stats",
                type: "GET",
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#cacheStats").text(result.data);
                    } else {
                        $("#cacheStats").text("获取缓存统计信息失败: " + result.msg);
                    }
                },
                error: function() {
                    $("#cacheStats").text("获取缓存统计信息失败: 网络错误");
                }
            });
        }

        // 清除所有缓存
        function clearAllCache() {
            $.modal.confirm("确定要清除所有缓存吗？", function() {
                $.ajax({
                    url: prefix + "/clear",
                    type: "DELETE",
                    success: function(result) {
                        $.modal.msgSuccess(result.msg);
                        refreshStats();
                    }
                });
            });
        }

        // 清除合作伙伴缓存
        function clearPartnerCache() {
            var partnerId = $("#partnerIdInput").val();
            if (!partnerId) {
                $.modal.alertWarning("请输入合作伙伴ID");
                return;
            }
            $.ajax({
                url: prefix + "/partner/" + partnerId,
                type: "DELETE",
                success: function(result) {
                    $.modal.msgSuccess(result.msg);
                    $("#partnerIdInput").val("");
                    refreshStats();
                }
            });
        }

        // 清除合作伙伴Key缓存
        function clearPartnerKeyCache() {
            var partnerKey = $("#partnerKeyInput").val();
            if (!partnerKey) {
                $.modal.alertWarning("请输入合作伙伴Key");
                return;
            }
            $.ajax({
                url: prefix + "/partner/key/" + encodeURIComponent(partnerKey),
                type: "DELETE",
                success: function(result) {
                    $.modal.msgSuccess(result.msg);
                    $("#partnerKeyInput").val("");
                    refreshStats();
                }
            });
        }

        // 清除广告信息缓存
        function clearAdsInfoCache() {
            var adsId = $("#adsIdInput").val();
            if (!adsId) {
                $.modal.alertWarning("请输入广告ID");
                return;
            }
            $.ajax({
                url: prefix + "/ads/" + adsId,
                type: "DELETE",
                success: function(result) {
                    $.modal.msgSuccess(result.msg);
                    $("#adsIdInput").val("");
                    refreshStats();
                }
            });
        }

        // 清除发布者配置缓存
        function clearPublisherOfferCache() {
            var offerId = $("#offerIdInput").val();
            if (!offerId) {
                $.modal.alertWarning("请输入配置ID");
                return;
            }
            $.ajax({
                url: prefix + "/publisher-offer/" + offerId,
                type: "DELETE",
                success: function(result) {
                    $.modal.msgSuccess(result.msg);
                    refreshStats();
                }
            });
        }

        // 清除广告主配置缓存
        function clearAdvertiserOfferCache() {
            var offerId = $("#offerIdInput").val();
            if (!offerId) {
                $.modal.alertWarning("请输入配置ID");
                return;
            }
            $.ajax({
                url: prefix + "/advertiser-offer/" + offerId,
                type: "DELETE",
                success: function(result) {
                    $.modal.msgSuccess(result.msg);
                    refreshStats();
                }
            });
        }
    </script>
</body>
</html>
