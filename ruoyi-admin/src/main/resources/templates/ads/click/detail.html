<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('点击记录详情')" />
    <style>
        .detail-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .detail-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
        }
        .detail-body {
            padding: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            color: #333;
            width: 150px;
            flex-shrink: 0;
        }
        .info-value {
            color: #666;
            flex: 1;
            word-break: break-all;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .url-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="detail-card">
            <div class="detail-header">
                <h3><i class="fa fa-mouse-pointer"></i> 点击记录详情</h3>
                <p>记录ID: <span th:text="${adsClickRecord.recordId}"></span></p>
            </div>

            <div class="detail-body">
                <!-- 基本信息 -->
                <div class="row">
                    <div class="col-md-6">
                        <h4><i class="fa fa-info-circle"></i> 基本信息</h4>
                        <div class="info-row">
                            <div class="info-label">点击ID:</div>
                            <div class="info-value" th:text="${adsClickRecord.clickId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">配置ID:</div>
                            <div class="info-value" th:text="${adsClickRecord.offerId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">设备ID:</div>
                            <div class="info-value" th:text="${adsClickRecord.deviceId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">IP地址:</div>
                            <div class="info-value" th:text="${adsClickRecord.ipAddress}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">国家代码:</div>
                            <div class="info-value" th:text="${adsClickRecord.countryCode}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">渠道:</div>
                            <div class="info-value" th:text="${adsClickRecord.channel}">-</div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h4><i class="fa fa-cogs"></i> 参数信息</h4>
                        <div class="info-row">
                            <div class="info-label">附属参数1:</div>
                            <div class="info-value" th:text="${adsClickRecord.subParam1}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">附属参数2:</div>
                            <div class="info-value" th:text="${adsClickRecord.subParam2}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">附属参数3:</div>
                            <div class="info-value" th:text="${adsClickRecord.subParam3}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">单价:</div>
                            <div class="info-value">
                                $<span th:text="${#numbers.formatDecimal(adsClickRecord.payout, 1, 4)}">0.0000</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">点击时间:</div>
                            <div class="info-value" th:text="${#dates.format(adsClickRecord.clickTime, 'yyyy-MM-dd HH:mm:ss')}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">状态:</div>
                            <div class="info-value">
                                <span th:if="${adsClickRecord.status == '0'}" class="status-badge status-success">正常</span>
                                <span th:if="${adsClickRecord.status == '1'}" class="status-badge status-error">异常</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 合作伙伴信息 -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-6">
                        <h4><i class="fa fa-users"></i> 开发者信息</h4>
                        <div class="info-row">
                            <div class="info-label">开发者ID:</div>
                            <div class="info-value" th:text="${adsClickRecord.publisherId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">开发者名称:</div>
                            <div class="info-value" th:text="${adsClickRecord.publisherName}">-</div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h4><i class="fa fa-building"></i> 广告主信息</h4>
                        <div class="info-row">
                            <div class="info-label">广告主ID:</div>
                            <div class="info-value" th:text="${adsClickRecord.advertiserId}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">广告主名称:</div>
                            <div class="info-value" th:text="${adsClickRecord.advertiserName}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">广告名称:</div>
                            <div class="info-value" th:text="${adsClickRecord.adsName}">-</div>
                        </div>
                    </div>
                </div>

                <!-- URL信息 -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12">
                        <h4><i class="fa fa-link"></i> URL信息</h4>
                        <div class="info-row">
                            <div class="info-label">目标URL:</div>
                            <div class="info-value">
                                <div class="url-box" th:text="${adsClickRecord.targetUrl}">-</div>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">User-Agent:</div>
                            <div class="info-value">
                                <div class="url-box" th:text="${adsClickRecord.userAgent}">-</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12">
                        <h4><i class="fa fa-info"></i> 系统信息</h4>
                        <div class="info-row">
                            <div class="info-label">创建时间:</div>
                            <div class="info-value" th:text="${#dates.format(adsClickRecord.createTime, 'yyyy-MM-dd HH:mm:ss')}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">更新时间:</div>
                            <div class="info-value" th:text="${#dates.format(adsClickRecord.updateTime, 'yyyy-MM-dd HH:mm:ss')}">-</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">备注:</div>
                            <div class="info-value" th:text="${adsClickRecord.remark}">-</div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12 text-center">
                        <button type="button" class="btn btn-primary" onclick="viewCallbacks()">
                            <i class="fa fa-list"></i> 查看回调记录
                        </button>
                        <button type="button" class="btn btn-default" onclick="$.modal.close()">
                            <i class="fa fa-close"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var clickId = [[${adsClickRecord.clickId}]];

        function viewCallbacks() {
            var url = ctx + "ads/callback/records?clickId=" + clickId;
            $.modal.openTab("回调记录", url);
        }
    </script>
</body>
</html>