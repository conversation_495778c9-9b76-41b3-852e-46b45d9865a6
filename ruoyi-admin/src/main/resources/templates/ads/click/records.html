<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('点击记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>点击ID：</label>
                                <input type="text" name="clickId"/>
                            </li>
                            <li>
                                <label>广告主：</label>
                                <select name="advertiserId" id="searchAdvertiserId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>开发者：</label>
                                <select name="publisherId" id="searchPublisherId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <label>设备ID：</label>
                                <input type="text" name="deviceId"/>
                            </li>
                            <li>
                                <label>IP地址：</label>
                                <input type="text" name="ipAddress"/>
                            </li>
                            <li>
                                <label>国家代码：</label>
                                <input type="text" name="countryCode"/>
                            </li>
                            <li>
                                <label>渠道：</label>
                                <input type="text" name="channel"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status">
                                    <option value="">所有</option>
                                    <option value="0">正常</option>
                                    <option value="1">异常</option>
                                </select>
                            </li>
                            <li>
                                <label>点击时间：</label>
                                <input type="text" class="time-input" placeholder="请选择开始时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" placeholder="请选择结束时间" name="params[endTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ads:click:records">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="showStatistics()">
                    <i class="fa fa-bar-chart"></i> 统计
                </a>
                <a class="btn btn-danger" onclick="cleanExpiredRecords()" shiro:hasPermission="ads:click:records">
                    <i class="fa fa-trash"></i> 清理过期记录
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "ads/click/record";

        $(function() {
            var options = {
                url: prefix + "/list",
                detailUrl: "ads/click/detail/{id}",
                exportUrl: prefix + "/export",
                modalName: "点击记录",
                showRefresh: true,
                showToggle: true,
                showColumns: true,
                rememberSelected: true,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'recordId',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'clickId',
                    title: '点击ID',
                    sortable: true,
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'adsName',
                    title: '广告名称',
                    sortable: true
                },
                {
                    field: 'advertiserName',
                    title: '广告主'
                },
                {
                    field: 'publisherName',
                    title: '开发者'
                },
                {
                    field: 'deviceId',
                    title: '设备ID',
                    formatter: function(value, row, index) {
                        if (value && value.length > 15) {
                            return '<span title="' + value + '">' + value.substring(0, 15) + '...</span>';
                        }
                        return value || '-';
                    }
                },
                {
                    field: 'ipAddress',
                    title: 'IP地址'
                },
                {
                    field: 'countryCode',
                    title: '国家'
                },
                {
                    field: 'channel',
                    title: '渠道'
                },
                {
                    field: 'payout',
                    title: '单价($)',
                    formatter: function(value, row, index) {
                        // return value ? '$' + value.toFixed(2) : '$0.00';
                        // 先判断是否为有效数字
                        if (value !== null && value !== undefined && !isNaN(value)) {
                            const num = parseFloat(value);
                            if (!isNaN(num)) {
                                return '$' + num.toFixed(2);
                            }
                        }
                        return '$0.00';
                    }
                },
                {
                    field: 'clickTime',
                    title: '点击时间',
                    sortable: true
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '0') {
                            return '<span class="label label-success">正常</span>';
                        } else if (value == '1') {
                            return '<span class="label label-danger">异常</span>';
                        }
                        return value;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.recordId + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="$.operate.detail(\'' + row.recordId + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewCallbacks(\'' + row.clickId + '\')"><i class="fa fa-list"></i>回调</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 加载广告主列表
            $.get(prefix + "/advertiserList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchAdvertiserId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });

            // 加载开发者列表
            $.get(prefix + "/publisherList", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    var $select = $("#searchPublisherId");
                    for (var i = 0; i < data.length; i++) {
                        $select.append('<option value="' + data[i].partnerId + '">' + data[i].partnerName + '</option>');
                    }
                }
            });
        });

        // 查看回调记录
        function viewCallbacks(clickId) {
            var url = ctx + "ads/callback/records?clickId=" + clickId;
            $.modal.openTab("回调记录", url);
        }

        // 显示统计信息
        function showStatistics() {
            $.get(prefix + "/statistics", function(result) {
                if (result.code == web_status.SUCCESS) {
                    var totalClicks = result.data.totalClicks;
                    var content = '<div class="text-center">' +
                                 '<h3>点击统计</h3>' +
                                 '<p class="text-info">总点击数：<strong>' + totalClicks + '</strong></p>' +
                                 '</div>';
                    $.modal.open("点击统计", content);
                }
            });
        }

        // 清理过期记录
        function cleanExpiredRecords() {
            $.modal.confirm("确定要清理30天前的过期记录吗？", function() {
                $.post(prefix + "/clean/30", function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>
