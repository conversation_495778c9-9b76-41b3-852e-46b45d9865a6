# 生产环境配置
server:
  # 服务器的HTTP端口
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数
    accept-count: 2000
    threads:
      # tomcat最大线程数
      max: 1000
      # Tomcat启动初始化的线程数
      min-spare: 200

# 日志配置
logging:
  level:
    com.ruoyi: info
    org.springframework: warn
    com.ruoyi.ads: info
    root: warn
  config: classpath:logback.xml

# Spring配置
spring:
  # 模板引擎
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 启用缓存
    cache: true
  # 服务模块
  devtools:
    restart:
      # 关闭热部署
      enabled: false
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: *********************************************************************************************************************************************************
        username: mobinovax_master
        password: 3BCDNzSiwcTnzdLD
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url: 
        username: 
        password: 
      # 初始连接数
      initialSize: 10
      # 最小连接池数量
      minIdle: 20
      # 最大连接池数量
      maxActive: 100
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=2000
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow: 127.0.0.1,192.168.0.0/16
        url-pattern: "/druid/*"
        # 控制台管理用户名和密码
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:your_druid_password}
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 2000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

# 项目相关配置
ruoyi:
  # 名称
  name: MobinovaX广告管理系统
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2025
  # 实例演示开关
  demoEnabled: false
  # 文件路径
  profile: /opt/mobinovax/uploadPath
  # 获取ip地址开关
  addressEnabled: true
  # 服务器域名
  domain: https://api.mobinovax.com
  # 日志路径
  log: /www/wwwroot/mobinovax_master/logs

# 用户配置
user:
  password:
    # 密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 3

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/ads/*

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false
  # 请求前缀
  pathMapping: /prod-api

# 广告系统配置
ads:
  # 点击追踪配置
  click:
    # 防重复请求时间窗口（毫秒）
    duplicate-window: 1000
    # 缓存清理间隔（毫秒）
    cache-cleanup-interval: 300000
  # 异步处理配置
  async:
    # 点击处理线程池
    click:
      core-pool-size: 10
      max-pool-size: 50
      queue-capacity: 500
    # 回调处理线程池
    callback:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 200
