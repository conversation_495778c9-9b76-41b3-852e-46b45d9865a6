package com.ruoyi.web.service;

import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 系统日志查看服务
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Service
public class LogViewService {

    private static final Logger logger = LoggerFactory.getLogger(LogViewService.class);

    @Value("${ruoyi.log:/www/wwwlogs/java/springboot}")
    private String logPath;

    // 如果配置的路径不存在，尝试使用相对路径
    private String getActualLogPath() {
        Path configuredPath = Paths.get(logPath);
        if (Files.exists(configuredPath)) {
            return logPath;
        }

        // 尝试使用项目根目录下的logs目录
        String projectLogPath = System.getProperty("user.dir") + File.separator + "logs";
        Path projectPath = Paths.get(projectLogPath);
        if (Files.exists(projectPath)) {
            return projectLogPath;
        }

        // 返回配置的路径（即使不存在）
        return logPath;
    }

    /**
     * 获取日志文件列表
     */
    public List<Map<String, Object>> getLogFiles() {
        List<Map<String, Object>> fileList = new ArrayList<>();
        
        try {
            String actualLogPath = getActualLogPath();
            Path logDir = Paths.get(actualLogPath);
            if (!Files.exists(logDir)) {
                logger.warn("日志目录不存在: {}", actualLogPath);
                return fileList;
            }

            try (Stream<Path> paths = Files.walk(logDir, 1)) {
                List<Path> logFiles = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".log"))
                    .sorted((p1, p2) -> {
                        try {
                            return Files.getLastModifiedTime(p2).compareTo(Files.getLastModifiedTime(p1));
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .collect(Collectors.toList());

                for (Path file : logFiles) {
                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("fileName", file.getFileName().toString());
                    fileInfo.put("filePath", file.toString());
                    fileInfo.put("fileSize", formatFileSize(Files.size(file)));
                    fileInfo.put("lastModified", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, 
                        new Date(Files.getLastModifiedTime(file).toMillis())));
                    fileList.add(fileInfo);
                }
            }
        } catch (IOException e) {
            logger.error("获取日志文件列表失败", e);
        }

        return fileList;
    }

    /**
     * 读取日志内容（分页）
     */
    public TableDataInfo readLogContent(String fileName, PageDomain pageDomain, String startDate, 
                                       String endDate, String logLevel, String keyword) {
        TableDataInfo dataTable = new TableDataInfo();
        List<Map<String, Object>> logLines = new ArrayList<>();
        
        try {
            String actualLogPath = getActualLogPath();
            Path logFile = Paths.get(actualLogPath, fileName);
            if (!Files.exists(logFile)) {
                logger.warn("日志文件不存在: {}", logFile);
                dataTable.setRows(logLines);
                dataTable.setTotal(0);
                return dataTable;
            }

            // 读取所有行
            List<String> allLines = Files.readAllLines(logFile, StandardCharsets.UTF_8);
            
            // 过滤日志行
            List<String> filteredLines = filterLogLines(allLines, startDate, endDate, logLevel, keyword);
            
            // 分页处理
            int pageNum = pageDomain.getPageNum();
            int pageSize = pageDomain.getPageSize();
            int total = filteredLines.size();
            
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);
            
            if (startIndex < total) {
                List<String> pageLines = filteredLines.subList(startIndex, endIndex);
                
                for (int i = 0; i < pageLines.size(); i++) {
                    Map<String, Object> logLine = parseLogLine(pageLines.get(i), startIndex + i + 1);
                    logLines.add(logLine);
                }
            }
            
            dataTable.setRows(logLines);
            dataTable.setTotal(total);
            dataTable.setCode(0);
            
        } catch (IOException e) {
            logger.error("读取日志文件失败: {}", fileName, e);
            dataTable.setCode(500);
            dataTable.setMsg("读取日志文件失败: " + e.getMessage());
        }
        
        return dataTable;
    }

    /**
     * 过滤日志行
     */
    private List<String> filterLogLines(List<String> allLines, String startDate, String endDate, 
                                       String logLevel, String keyword) {
        return allLines.stream()
            .filter(line -> filterByDate(line, startDate, endDate))
            .filter(line -> filterByLogLevel(line, logLevel))
            .filter(line -> filterByKeyword(line, keyword))
            .collect(Collectors.toList());
    }

    /**
     * 按日期过滤
     */
    private boolean filterByDate(String line, String startDate, String endDate) {
        if (StringUtils.isEmpty(startDate) && StringUtils.isEmpty(endDate)) {
            return true;
        }
        
        // 提取日志行中的日期时间 (格式: 2025-01-28 14:30:25.123)
        Pattern datePattern = Pattern.compile("^(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2})");
        java.util.regex.Matcher matcher = datePattern.matcher(line);
        
        if (!matcher.find()) {
            return true; // 如果无法解析日期，则不过滤
        }
        
        try {
            String logDateTime = matcher.group(1);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date logDate = sdf.parse(logDateTime);
            
            if (StringUtils.isNotEmpty(startDate)) {
                Date start = DateUtils.parseDate(startDate + " 00:00:00");
                if (logDate.before(start)) {
                    return false;
                }
            }
            
            if (StringUtils.isNotEmpty(endDate)) {
                Date end = DateUtils.parseDate(endDate + " 23:59:59");
                if (logDate.after(end)) {
                    return false;
                }
            }
            
        } catch (Exception e) {
            logger.debug("解析日志日期失败: {}", line);
        }
        
        return true;
    }

    /**
     * 按日志级别过滤
     */
    private boolean filterByLogLevel(String line, String logLevel) {
        if (StringUtils.isEmpty(logLevel)) {
            return true;
        }
        
        return line.contains(" " + logLevel.toUpperCase() + " ");
    }

    /**
     * 按关键字过滤
     */
    private boolean filterByKeyword(String line, String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return true;
        }
        
        return line.toLowerCase().contains(keyword.toLowerCase());
    }

    /**
     * 解析日志行
     */
    private Map<String, Object> parseLogLine(String line, int lineNumber) {
        Map<String, Object> logLine = new HashMap<>();
        logLine.put("lineNumber", lineNumber);
        logLine.put("content", line);
        
        // 解析日志级别
        if (line.contains(" ERROR ")) {
            logLine.put("level", "ERROR");
            logLine.put("levelClass", "danger");
        } else if (line.contains(" WARN ")) {
            logLine.put("level", "WARN");
            logLine.put("levelClass", "warning");
        } else if (line.contains(" INFO ")) {
            logLine.put("level", "INFO");
            logLine.put("levelClass", "info");
        } else if (line.contains(" DEBUG ")) {
            logLine.put("level", "DEBUG");
            logLine.put("levelClass", "success");
        } else {
            logLine.put("level", "UNKNOWN");
            logLine.put("levelClass", "default");
        }
        
        // 解析时间戳
        Pattern timePattern = Pattern.compile("^(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3})");
        java.util.regex.Matcher timeMatcher = timePattern.matcher(line);
        if (timeMatcher.find()) {
            logLine.put("timestamp", timeMatcher.group(1));
        }
        
        return logLine;
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 清理过期日志文件
     */
    public boolean cleanOldLogs(int days) {
        try {
            String actualLogPath = getActualLogPath();
            Path logDir = Paths.get(actualLogPath);
            if (!Files.exists(logDir)) {
                return false;
            }

            LocalDate cutoffDate = LocalDate.now().minusDays(days);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            
            try (Stream<Path> paths = Files.walk(logDir, 1)) {
                List<Path> filesToDelete = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".log"))
                    .filter(path -> {
                        String fileName = path.getFileName().toString();
                        // 匹配日期格式的日志文件 (如: sys-info.2025-01-20.log)
                        Pattern datePattern = Pattern.compile(".*\\.(\\d{4}-\\d{2}-\\d{2})\\.log$");
                        java.util.regex.Matcher matcher = datePattern.matcher(fileName);
                        if (matcher.find()) {
                            try {
                                LocalDate fileDate = LocalDate.parse(matcher.group(1), formatter);
                                return fileDate.isBefore(cutoffDate);
                            } catch (Exception e) {
                                return false;
                            }
                        }
                        return false;
                    })
                    .collect(Collectors.toList());

                for (Path file : filesToDelete) {
                    try {
                        Files.delete(file);
                        logger.info("删除过期日志文件: {}", file.getFileName());
                    } catch (IOException e) {
                        logger.error("删除日志文件失败: {}", file.getFileName(), e);
                    }
                }
                
                return true;
            }
        } catch (IOException e) {
            logger.error("清理日志文件失败", e);
            return false;
        }
    }
}
