package com.ruoyi.web.controller.tool;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.web.service.LogViewService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * 系统日志查看控制器
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Controller
@RequestMapping("/tool/log")
public class LogViewController extends BaseController {

    private String prefix = "tool/log";

    @Autowired
    private LogViewService logViewService;

    /**
     * 系统日志页面
     */
    @RequiresPermissions("tool:log:view")
    @GetMapping()
    public String log(ModelMap mmap) {
        // 获取日志文件列表
        List<Map<String, Object>> logFiles = logViewService.getLogFiles();
        mmap.put("logFiles", logFiles);
        return prefix + "/log";
    }

    /**
     * 获取日志文件列表
     */
    @RequiresPermissions("tool:log:list")
    @PostMapping("/files")
    @ResponseBody
    public AjaxResult getLogFiles() {
        try {
            List<Map<String, Object>> logFiles = logViewService.getLogFiles();
            return AjaxResult.success(logFiles);
        } catch (Exception e) {
            logger.error("获取日志文件列表失败", e);
            return AjaxResult.error("获取日志文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询日志内容
     */
    @RequiresPermissions("tool:log:list")
    @PostMapping("/content")
    @ResponseBody
    public TableDataInfo getLogContent(@RequestParam("fileName") String fileName,
                                      @RequestParam(value = "startDate", required = false) String startDate,
                                      @RequestParam(value = "endDate", required = false) String endDate,
                                      @RequestParam(value = "logLevel", required = false) String logLevel,
                                      @RequestParam(value = "keyword", required = false) String keyword) {
        try {
            // 验证文件名安全性
            if (StringUtils.isEmpty(fileName) || fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                TableDataInfo errorTable = new TableDataInfo();
                errorTable.setCode(500);
                errorTable.setMsg("无效的文件名");
                return errorTable;
            }

            PageDomain pageDomain = TableSupport.buildPageRequest();
            return logViewService.readLogContent(fileName, pageDomain, startDate, endDate, logLevel, keyword);
        } catch (Exception e) {
            logger.error("读取日志内容失败: {}", fileName, e);
            TableDataInfo errorTable = new TableDataInfo();
            errorTable.setCode(500);
            errorTable.setMsg("读取日志内容失败: " + e.getMessage());
            return errorTable;
        }
    }

    /**
     * 下载日志文件
     */
    @Log(title = "系统日志", businessType = BusinessType.EXPORT)
    @RequiresPermissions("tool:log:download")
    @GetMapping("/download")
    public void downloadLogFile(@RequestParam("fileName") String fileName, HttpServletResponse response) {
        try {
            // 验证文件名安全性
            if (StringUtils.isEmpty(fileName) || fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("无效的文件名");
                return;
            }

            // 使用LogViewService来获取正确的日志路径
            List<Map<String, Object>> logFiles = logViewService.getLogFiles();
            String filePath = null;
            for (Map<String, Object> file : logFiles) {
                if (fileName.equals(file.get("fileName"))) {
                    filePath = (String) file.get("filePath");
                    break;
                }
            }

            if (filePath == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("文件不存在");
                return;
            }

            Path logFile = Paths.get(filePath);
            if (!Files.exists(logFile)) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("文件不存在");
                return;
            }

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", 
                "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentLengthLong(Files.size(logFile));

            // 输出文件内容
            try (InputStream inputStream = Files.newInputStream(logFile);
                 OutputStream outputStream = response.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }

            logger.info("下载日志文件: {}", fileName);

        } catch (Exception e) {
            logger.error("下载日志文件失败: {}", fileName, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("下载失败: " + e.getMessage());
            } catch (IOException ioException) {
                logger.error("写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 清理过期日志
     */
    @Log(title = "系统日志", businessType = BusinessType.CLEAN)
    @RequiresPermissions("tool:log:clean")
    @PostMapping("/clean")
    @ResponseBody
    public AjaxResult cleanOldLogs(@RequestParam(value = "days", defaultValue = "30") int days) {
        try {
            if (days < 1 || days > 365) {
                return AjaxResult.error("清理天数必须在1-365之间");
            }

            boolean success = logViewService.cleanOldLogs(days);
            if (success) {
                logger.info("清理{}天前的日志文件成功", days);
                return AjaxResult.success("清理日志文件成功");
            } else {
                return AjaxResult.error("清理日志文件失败");
            }
        } catch (Exception e) {
            logger.error("清理日志文件失败", e);
            return AjaxResult.error("清理日志文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取日志统计信息
     */
    @RequiresPermissions("tool:log:list")
    @PostMapping("/stats")
    @ResponseBody
    public AjaxResult getLogStats(@RequestParam("fileName") String fileName) {
        try {
            // 验证文件名安全性
            if (StringUtils.isEmpty(fileName) || fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                return AjaxResult.error("无效的文件名");
            }

            // 使用LogViewService来获取正确的日志路径
            List<Map<String, Object>> logFiles = logViewService.getLogFiles();
            String filePath = null;
            for (Map<String, Object> file : logFiles) {
                if (fileName.equals(file.get("fileName"))) {
                    filePath = (String) file.get("filePath");
                    break;
                }
            }

            if (filePath == null) {
                return AjaxResult.error("文件不存在");
            }

            Path logFile = Paths.get(filePath);
            if (!Files.exists(logFile)) {
                return AjaxResult.error("文件不存在");
            }

            // 读取文件统计信息
            long fileSize = Files.size(logFile);
            long lineCount = Files.lines(logFile).count();
            
            // 统计各级别日志数量
            List<String> lines = Files.readAllLines(logFile);
            long errorCount = lines.stream().mapToLong(line -> line.contains(" ERROR ") ? 1 : 0).sum();
            long warnCount = lines.stream().mapToLong(line -> line.contains(" WARN ") ? 1 : 0).sum();
            long infoCount = lines.stream().mapToLong(line -> line.contains(" INFO ") ? 1 : 0).sum();
            long debugCount = lines.stream().mapToLong(line -> line.contains(" DEBUG ") ? 1 : 0).sum();

            Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("fileSize", formatFileSize(fileSize));
            stats.put("lineCount", lineCount);
            stats.put("errorCount", errorCount);
            stats.put("warnCount", warnCount);
            stats.put("infoCount", infoCount);
            stats.put("debugCount", debugCount);

            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取日志统计信息失败: {}", fileName, e);
            return AjaxResult.error("获取日志统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
