package com.ruoyi.web.controller.ads;

import com.ruoyi.ads.domain.*;
import com.ruoyi.ads.service.*;
import com.ruoyi.common.core.controller.BaseController;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 广告管理页面控制器
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Controller
public class AdsViewController extends BaseController
{
    private String prefix = "ads";

    @Autowired
    private IAdsInfoService adsInfoService;
    
    @Autowired
    private IAdsPartnerService adsPartnerService;
    
    @Autowired
    private IAdsPublisherOfferService adsPublisherOfferService;
    
    @Autowired
    private IAdsClickRecordService adsClickRecordService;
    
    @Autowired
    private IAdsCallbackRecordService adsCallbackRecordService;

    /**
     * 广告信息页面
     */
    @RequiresPermissions("ads:info:view")
    @GetMapping("/ads/info")
    public String info()
    {
        return prefix + "/info/info";
    }

    /**
     * 新增广告信息页面
     */
    @GetMapping("/ads/info/add")
    public String add()
    {
        return prefix + "/info/add";
    }

    /**
     * 查看广告信息页面
     */
    @GetMapping("/ads/info/detail/{adsId}")
    public String detail(@PathVariable("adsId") Long adsId, ModelMap mmap)
    {
        AdsInfo adsInfo = adsInfoService.selectAdsInfoByAdsId(adsId);
        mmap.put("adsInfo", adsInfo);
        return prefix + "/info/detail";
    }

    /**
     * 修改广告信息页面
     */
    @GetMapping("/ads/info/edit/{adsId}")
    public String edit(@PathVariable("adsId") Long adsId, ModelMap mmap)
    {
        AdsInfo adsInfo = adsInfoService.selectAdsInfoByAdsId(adsId);
        mmap.put("adsInfo", adsInfo);
        return prefix + "/info/edit";
    }

    /**
     * 合作伙伴页面
     */
    @RequiresPermissions("ads:partner:view")
    @GetMapping("/ads/partner")
    public String partner()
    {
        return prefix + "/partner/partner";
    }

    /**
     * 新增合作伙伴页面
     */
    @GetMapping("/ads/partner/add")
    public String addPartner()
    {
        return prefix + "/partner/add";
    }

    /**
     * 查看合作伙伴页面
     */
    @GetMapping("/ads/partner/detail/{partnerId}")
    public String detailPartner(@PathVariable("partnerId") Long partnerId, ModelMap mmap)
    {
        AdsPartner adsPartner = adsPartnerService.selectAdsPartnerByPartnerId(partnerId);
        mmap.put("adsPartner", adsPartner);
        return prefix + "/partner/detail";
    }

    /**
     * 修改合作伙伴页面
     */
    @GetMapping("/ads/partner/edit/{partnerId}")
    public String editPartner(@PathVariable("partnerId") Long partnerId, ModelMap mmap)
    {
        AdsPartner adsPartner = adsPartnerService.selectAdsPartnerByPartnerId(partnerId);
        mmap.put("adsPartner", adsPartner);
        return prefix + "/partner/edit";
    }

    /**
     * 广告配置页面
     */
    @RequiresPermissions("ads:offer:view")
    @GetMapping("/ads/offer")
    public String offer()
    {
        return prefix + "/offer/offer";
    }

    /**
     * 新增广告配置页面
     */
    @GetMapping("/ads/offer/add")
    public String addOffer()
    {
        return prefix + "/offer/add";
    }

    /**
     * 查看广告配置页面
     */
    @GetMapping("/ads/offer/detail/{offerId}")
    public String detailOffer(@PathVariable("offerId") Long offerId, ModelMap mmap)
    {
        AdsPublisherOffer adsPublisherOffer = adsPublisherOfferService.selectAdsPublisherOfferWithRelatedByOfferId(offerId);
        mmap.put("adsPublisherOffer", adsPublisherOffer);
        return prefix + "/offer/detail";
    }

    /**
     * 修改广告配置页面
     */
    @GetMapping("/ads/offer/edit/{offerId}")
    public String editOffer(@PathVariable("offerId") Long offerId, ModelMap mmap)
    {
        AdsPublisherOffer adsPublisherOffer = adsPublisherOfferService.selectAdsPublisherOfferWithRelatedByOfferId(offerId);
        mmap.put("adsPublisherOffer", adsPublisherOffer);
        return prefix + "/offer/edit";
    }

    /**
     * 修改广告配置页面
     */
    //@GetMapping("/ads/offer/edit/{offerId}")
    //public String editOffer(@PathVariable("offerId") Long offerId, ModelMap mmap)
    //{
    //    AdsPublisherOffer adsPublisherOffer = adsPublisherOfferService.selectAdsPublisherOfferByOfferId(offerId);
    //    mmap.put("adsPublisherOffer", adsPublisherOffer);
    //    return prefix + "/offer/edit";
    //}

    /**
     * 点击记录页面
     */
    @RequiresPermissions("ads:click:records")
    @GetMapping("/ads/click/records")
    public String clickRecords()
    {
        return prefix + "/click/records";
    }

    /**
     * 查看点击信息页面
     */
    @GetMapping("/ads/click/detail/{recordId}")
    public String clickDetail(@PathVariable("recordId") Long recordId, ModelMap mmap)
    {
        AdsClickRecord adsClickRecord = adsClickRecordService.selectAdsClickRecordByRecordId(recordId);
        mmap.put("adsClickRecord", adsClickRecord);
        return prefix + "/click/detail";
    }

    /**
     * 回调记录页面
     */
    @RequiresPermissions("ads:callback:records")
    @GetMapping("/ads/callback/records")
    public String callbackRecords()
    {
        return prefix + "/callback/records";
    }

    /**
     * 查看回调信息页面
     */
    @GetMapping("/ads/callback/detail/{recordId}")
    public String callbackDetail(@PathVariable("recordId") Long recordId, ModelMap mmap)
    {
        AdsCallbackRecord adsClickRecord = adsCallbackRecordService.selectAdsCallbackRecordByRecordId(recordId);
        mmap.put("adsCallbackRecord", adsClickRecord);
        return prefix + "/callback/detail";
    }

    /**
     * 点击统计页面
     */
    @RequiresPermissions("ads:click:statistics")
    @GetMapping("/ads/click/statistics")
    public String clickStatistics()
    {
        return prefix + "/statistics/click";
    }

    /**
     * 回调统计页面
     */
    @RequiresPermissions("ads:callback:statistics")
    @GetMapping("/ads/callback/statistics")
    public String callbackStatistics()
    {
        return prefix + "/statistics/callback";
    }

    /**
     * 统计API测试页面
     */
    @GetMapping("/ads/statistics/test")
    public String statisticsTest()
    {
        return prefix + "/statistics/test";
    }

    /**
     * 缓存配置页面
     */
    @RequiresPermissions("ads:cache:view")
    @GetMapping("/ads/cache")
    public String cache()
    {
        return prefix + "/cache/cache";
    }

}
